## 导入信息说明
com.sankuai.general.order.querycenter.api.service.OrderSearchService.searchByUserId
com.sankuai.general.order.querycenter.api.request.OrderSearchRequest
com.sankuai.general.order.querycenter.api.request.SessionContext
com.sankuai.general.order.querycenter.api.response.OrderSearchResponse

## 请求参数
- 是个数组，里面有多个对象，每个对象代表一个参数
[
    {
        "id": null,
        "name": "searchRequest",
        "type": "OrderSearchRequest",
        "required": true,
        "description": "订单检索请求参数",
        "example": null,
        "children": [
            {
                "id": null,
                "name": "pageNo",
                "type": "Integer",
                "required": true,
                "description": "页码，从0开始",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "pageSize",
                "type": "Integer",
                "required": true,
                "description": "每页大小，最大不超过限制值",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "needSearchAfter",
                "type": "Boolean",
                "required": true,
                "description": "是否使用searchAfter方式查询，默认false",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "orderSearchDetailRequestList",
                "type": "List<OrderSearchDetailRequest>",
                "required": false,
                "description": "详细检索条件列表，当needTemplateSearch=false时使用",
                "example": null,
                "children": [
                    {
                        "id": null,
                        "name": "data",
                        "type": "OrderSearchDetailRequest",
                        "required": null,
                        "description": "订单搜索详细请求",
                        "example": null,
                        "children": [
                            {
                                "id": null,
                                "name": "filterZeroAmountOrder",
                                "type": "boolean",
                                "required": false,
                                "description": "是否过滤掉0元单",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "filterEmptyMobileNo",
                                "type": "boolean",
                                "required": false,
                                "description": "是否过滤掉空手机号",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "displayModeList",
                                "type": "List<String>",
                                "required": false,
                                "description": "展示模式列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "excludeDisplayModeList",
                                "type": "List<String>",
                                "required": false,
                                "description": "排除展示模式列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "platformFlag",
                                "type": "Integer",
                                "required": false,
                                "description": "平台标识，参考PlatformFlagEnum",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "termSearchRequestList",
                                "type": "List<TermSearchRequest>",
                                "required": false,
                                "description": "精确查询条件列表",
                                "example": null,
                                "children": [
                                    {
                                        "id": null,
                                        "name": "data",
                                        "type": "TermSearchRequest",
                                        "required": null,
                                        "description": "等值条件查询",
                                        "example": null,
                                        "children": [
                                            {
                                                "id": null,
                                                "name": "fieldName",
                                                "type": "String",
                                                "required": true,
                                                "description": "查询字段名",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "fieldValues",
                                                "type": "Set<String>",
                                                "required": true,
                                                "description": "查询字段值集合",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "include",
                                                "type": "boolean",
                                                "required": false,
                                                "description": "true等价于IN查询，false等价于NOT IN查询，默认true",
                                                "example": null,
                                                "children": []
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "id": null,
                                "name": "matchSearchRequestList",
                                "type": "List<MatchSearchRequest>",
                                "required": false,
                                "description": "模糊查询条件列表",
                                "example": null,
                                "children": [
                                    {
                                        "id": null,
                                        "name": "data",
                                        "type": "MatchSearchRequest",
                                        "required": null,
                                        "description": "模糊查询",
                                        "example": null,
                                        "children": [
                                            {
                                                "id": null,
                                                "name": "fieldName",
                                                "type": "String",
                                                "required": true,
                                                "description": "查询字段名",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "fieldValue",
                                                "type": "String",
                                                "required": true,
                                                "description": "模糊查询值",
                                                "example": null,
                                                "children": []
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "id": null,
                                "name": "rangeSearchRequestList",
                                "type": "List<RangeSearchRequest>",
                                "required": false,
                                "description": "范围查询条件列表",
                                "example": null,
                                "children": [
                                    {
                                        "id": null,
                                        "name": "data",
                                        "type": "RangeSearchRequest",
                                        "required": null,
                                        "description": "范围查询",
                                        "example": null,
                                        "children": [
                                            {
                                                "id": null,
                                                "name": "fieldName",
                                                "type": "String",
                                                "required": true,
                                                "description": "查询字段名",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "from",
                                                "type": "String",
                                                "required": false,
                                                "description": "查询区间下限",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "to",
                                                "type": "String",
                                                "required": false,
                                                "description": "查询区间上限",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "includeLower",
                                                "type": "boolean",
                                                "required": false,
                                                "description": "是否包含区间下限，默认true",
                                                "example": null,
                                                "children": []
                                            },
                                            {
                                                "id": null,
                                                "name": "includeUpper",
                                                "type": "boolean",
                                                "required": false,
                                                "description": "是否包含区间上限，默认false",
                                                "example": null,
                                                "children": []
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "id": null,
                                "name": "termSearchExtraInfoRequestList",
                                "type": "List<TermSearchRequest>",
                                "required": false,
                                "description": "扩展字段精确查询条件列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "matchSearchExtraInfoRequestList",
                                "type": "List<MatchSearchRequest>",
                                "required": false,
                                "description": "扩展字段模糊查询条件列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "rangeSearchExtraInfoRequestList",
                                "type": "List<RangeSearchRequest>",
                                "required": false,
                                "description": "扩展字段范围查询条件列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "filterDefaultBizCode",
                                "type": "Boolean",
                                "required": false,
                                "description": "是否过滤掉默认bizcode，默认false",
                                "example": null,
                                "children": []
                            }
                        ]
                    }
                ]
            },
            {
                "id": null,
                "name": "orderSearchDetailTemplateRequestList",
                "type": "List<OrderSearchDetailTemplateRequest>",
                "required": false,
                "description": "模板检索条件列表，当needTemplateSearch=true时使用",
                "example": null,
                "children": [
                    {
                        "id": null,
                        "name": "data",
                        "type": "OrderSearchDetailTemplateRequest",
                        "required": null,
                        "description": "订单搜索模板详细请求",
                        "example": null,
                        "children": [
                            {
                                "id": null,
                                "name": "searchTemplateId",
                                "type": "Integer",
                                "required": true,
                                "description": "检索模版ID，对应bizType",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "orderSearchDetailRequestList",
                                "type": "List<OrderSearchDetailRequest>",
                                "required": false,
                                "description": "检索条件列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "sortFieldExtraInfoRequestList",
                                "type": "List<SortFieldRequest>",
                                "required": false,
                                "description": "扩展模板字段排序条件列表",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "needExtraInfoSortDefault",
                                "type": "boolean",
                                "required": false,
                                "description": "排序条件映射失败时是否走兜底逻辑，默认false",
                                "example": null,
                                "children": []
                            }
                        ]
                    }
                ]
            },
            {
                "id": null,
                "name": "sortFieldRequestList",
                "type": "List<SortFieldRequest>",
                "required": false,
                "description": "排序条件列表",
                "example": null,
                "children": [
                    {
                        "id": null,
                        "name": "data",
                        "type": "SortFieldRequest",
                        "required": null,
                        "description": "排序字段",
                        "example": null,
                        "children": [
                            {
                                "id": null,
                                "name": "fieldName",
                                "type": "String",
                                "required": true,
                                "description": "排序字段名",
                                "example": null,
                                "children": []
                            },
                            {
                                "id": null,
                                "name": "asc",
                                "type": "boolean",
                                "required": false,
                                "description": "是否升序，true-升序，false-降序，默认false",
                                "example": null,
                                "children": []
                            }
                        ]
                    }
                ]
            },
            {
                "id": null,
                "name": "returnFields",
                "type": "List<String>",
                "required": false,
                "description": "返回字段列表，支持枚举PlatformIndexKeyEnum",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "needTemplateSearch",
                "type": "Boolean",
                "required": true,
                "description": "是否支持模板查询，默认false",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "sortValues",
                "type": "List<String>",
                "required": false,
                "description": "search_after查询翻页的排序字段，查询非第一页数据时必填",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "trackTotalHits",
                "type": "Integer",
                "required": false,
                "description": "查询之后计算的命中数，不传默认1万",
                "example": null,
                "children": []
            }
        ]
    },
    {
        "id": null,
        "name": "context",
        "type": "SessionContext",
        "required": true,
        "description": "会话上下文",
        "example": null,
        "children": [
            {
                "id": null,
                "name": "bizLine",
                "type": "int",
                "required": true,
                "description": "业务身份，参考BizLineEnum",
                "example": null,
                "children": []
            }
        ]
    }
]

## 响应参数
- 是个数组，里面有多个对象，每个对象代表一个返回参数对象
[
    {
        "id": null,
        "name": "data",
        "type": "OrderSearchResponse",
        "required": null,
        "description": "订单检索结果",
        "example": null,
        "children": [
            {
                "id": null,
                "name": "code",
                "type": "int",
                "required": null,
                "description": "响应状态码",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "errMessage",
                "type": "String",
                "required": null,
                "description": "错误信息",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "data",
                "type": "List<Map<String, String>>",
                "required": null,
                "description": "订单数据列表，每个元素是键值对映射",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "totalHit",
                "type": "long",
                "required": null,
                "description": "总命中数",
                "example": null,
                "children": []
            },
            {
                "id": null,
                "name": "sortValues",
                "type": "List<String>",
                "required": null,
                "description": "排序值列表，用于search_after分页",
                "example": null,
                "children": []
            }
        ]
    }
]