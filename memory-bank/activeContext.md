# 当前工作上下文

## 当前工作重点
- 当前正在关注 DealStockBatchFetcher 类的 batchGet 方法，该方法负责批量获取团购库存信息
- 相关文件包括：
  - DealModelUtil.java
  - DealAvailableDateDocument.java
  - ProductDTO.java
  - DealBathProductTagsDocument.java

## 最近变更
- 尚未记录具体变更，项目处于初始分析阶段

## 下一步计划
- 深入理解团购库存批量获取的实现机制
- 分析团购产品属性文档的处理流程
- 了解团购交易数据的整体处理流程

## 活跃决策和考虑因素
- 代码注释使用中文
- 代码风格和命名风格需与现有代码保持一致
- 修改范围需严格控制，避免影响不相关的功能

## 重要模式和偏好
- 使用批量获取模式提高数据处理效率
- 采用文档处理模式统一管理产品属性
- 单元测试使用 Mockito 框架，测试方法名以 test 开头
- 编译命令：`mvn clean package -DskipTests -Denforcer.skip=true -U com.meituan.mdp.maven.plugins:mdp-maven-enforcer-plugin:1.0.0-SNAPSHOT:enforce com.dianping.maven.plugins:dependency-check-plugin:1.8.42:check -DappCheckEnv=test -Dchecklist=http://dependency.sankuai.com/dependency/getCompanyCheckList#http://dependency.sankuai.com/dependency/到店综合研发中心/test/getBGCheckList -Dmetadir=/tmp/build/src/.plus/metadata`
- 测试命令：`mvn clean test -DXms4096m`

## 项目洞察和学习
- 项目采用 Java 语言开发
- 使用 Maven 进行项目管理和构建
- 代码组织结构清晰，按功能模块划分
