# 技术上下文

## 使用的技术
- **编程语言**：Java
- **构建工具**：Maven
- **测试框架**：Mockito
- **项目管理**：Git

## 开发环境设置
- **JDK 版本**：根据项目配置
- **Maven 配置**：使用美团内部 Maven 配置
- **编译命令**：`mvn clean package -DskipTests -Denforcer.skip=true -U com.meituan.mdp.maven.plugins:mdp-maven-enforcer-plugin:1.0.0-SNAPSHOT:enforce com.dianping.maven.plugins:dependency-check-plugin:1.8.42:check -DappCheckEnv=test -Dchecklist=http://dependency.sankuai.com/dependency/getCompanyCheckList#http://dependency.sankuai.com/dependency/到店综合研发中心/test/getBGCheckList -Dmetadir=/tmp/build/src/.plus/metadata`
- **测试命令**：`mvn clean test -DXms4096m`

## 技术约束
- 代码注释使用中文
- 代码风格和命名风格需与现有代码保持一致
- 修改范围需严格控制，避免影响不相关的功能
- 单元测试文件名以 Test 结尾
- 单元测试方法名以 test 开头，使用驼峰命名法
- 单元测试中外部方法的调用使用 mock 对象

## 依赖关系
- 美团内部 Maven 依赖
- 美团内部服务依赖
- 第三方库依赖

## 工具使用模式
- **批量处理**：使用批量获取模式提高数据处理效率
- **文档处理**：使用文档处理模式统一管理产品属性
- **单元测试**：使用 Mockito 框架进行单元测试，包括静态方法的 mock
- **代码组织**：按功能模块划分代码结构，保持清晰的层次关系

## 开发规范
- Java 类的第一行应该是 package 声明
- 生成的代码应该包含注释
- 单元测试规范遵循项目要求
- 回复语言与文件类型保持一致，使用中文回复
