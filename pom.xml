<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai</groupId>
    <artifactId>dztheme-dealgroup-home</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>dztheme-dealgroup-home</name>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.7.17.3</version>
    </parent>


    <modules>
        <module>dztheme-dealgroup-api</module>
        <module>dztheme-dealgroup-service</module>
    </modules>

    <dependencies>
        <!-- mtrace相关依赖，目前需要人工指定mdp-parent 暂未升级 -->
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent-runtime</artifactId>
        </dependency>

        <!-- mtrace相关依赖结束 -->
        <dependency>
            <groupId>com.sankuai.mlive</groupId>
            <artifactId>mlive-cquery-api</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mlive</groupId>
            <artifactId>mlive-common-api</artifactId>
            <version>6.2.19</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.order</groupId>
            <artifactId>querycenter-api</artifactId>
            <version>1.0.18</version>
        </dependency>
    </dependencies>

    <properties>
        <mapi-log-util.version>2.0.1</mapi-log-util.version>
        <technician-biz-api.version>2.9.25</technician-biz-api.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!--玩法中心-->
            <dependency>
                <groupId>com.sankuai.mktplay</groupId>
                <artifactId>mkt-play-center-client</artifactId>
                <version>1.0.4</version>
            </dependency>
            <!--休娱-->
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-category-process-api</artifactId>
                <version>1.2.60</version>
            </dependency>
            <!--榜单标签-->
            <dependency>
                <groupId>com.sankuai.mdp</groupId>
                <artifactId>dzrank-scenes-api</artifactId>
                <version>0.0.29</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.insurance.adapter</groupId>
                <artifactId>insurance-adapter-dc-sdk</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>joy-general-api</artifactId>
                <version>0.0.61</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctmember</groupId>
                <artifactId>mpmctmember-process-common</artifactId>
                <version>0.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctmember</groupId>
                <artifactId>mpmctmember-process-thrift</artifactId>
                <version>0.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wpt.user.retrieve</groupId>
                <artifactId>retrieve-api</artifactId>
                <version>1.2.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>user-base-remote</artifactId>
                <version>2.1.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-sales-common-api</artifactId>
                <version>2.1.2</version>
            </dependency>
            <dependency>
                <artifactId>mpproduct-publish-common</artifactId>
                <groupId>com.sankuai.mpproduct.publish</groupId>
                <version>1.3.55</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-resource-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-aggregate-api</artifactId>
                <version>1.8.83</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.zdc</groupId>
                <artifactId>zdc-produce-api</artifactId>
                <version>0.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctcontent</groupId>
                <artifactId>mpmctcontent-query-thrift</artifactId>
                <version>0.4.3</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctexhibit</groupId>
                <artifactId>mpmctexhibit-process-thrift</artifactId>
                <version>0.4.26</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.beautycontent</groupId>
                <artifactId>security-api</artifactId>
                <version>0.1.19</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dztheme-dealgroup-service</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-reception-api</artifactId>
                <version>0.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>ugc-review-api</artifactId>
                <version>3.3.25</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.lingtai</groupId>
                <artifactId>com.sankuai.lingtai.tag.api</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.joynav</groupId>
                <artifactId>joynav-rb-api</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-activity-api</artifactId>
                <version>2.2.72</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-event-datapools-api</artifactId>
                <version>2.1.13</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.general</groupId>
                <artifactId>martgeneral-recommend-api</artifactId>
                <version>1.7.5</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzaggregation</groupId>
                <artifactId>dzaggregation-platformpoilist-api</artifactId>
                <version>0.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-sdk-shopuuid</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>ch.hsr</groupId>
                <artifactId>geohash</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>dentistry-book-api</artifactId>
                <version>1.0.58</version>
            </dependency>
            <dependency>
                <groupId>com.dp.search</groupId>
                <artifactId>mainshop-client</artifactId>
                <version>1.0.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cat-core</artifactId>
                        <groupId>com.dianping.cat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>arts-client</artifactId>
                        <groupId>com.dp.arts</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dp.arts</groupId>
                <artifactId>arts-client</artifactId>
                <version>1.5.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cat-core</artifactId>
                        <groupId>com.dianping.cat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-ugc-nr</artifactId>
                <version>0.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-inf-rpc</artifactId>
                <version>0.0.39</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-inf-cache</artifactId>
                <version>0.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-degrade-client</artifactId>
                <version>0.1.3</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>5.2.2.2.DP</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.10.19</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gateway-framework-web</artifactId>
                <version>1.0.48</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mapi-shell</artifactId>
                        <groupId>com.dianping.mobile</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.dp</groupId>
                <artifactId>gm-marketing-member-card-api</artifactId>
                <version>0.2.28</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-product-nr</artifactId>
                <version>0.0.21</version>
                <exclusions>
                    <exclusion>
                        <artifactId>joy-common-helper</artifactId>
                        <groupId>com.dianping.joy</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>sku-common</artifactId>
                        <groupId>com.dianping.tpfun</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>gm-bonus-exposure-api</artifactId>
                        <groupId>com.dianping.dp</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-underlayer-api</artifactId>
                <version>1.1.32</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-meta-tag-manage-api</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-test</artifactId>
                <version>0.0.18</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>beauty-tagsearch-api</artifactId>
                <version>1.1.11</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-mbop-bom</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>sandbox-runtime</artifactId>
                        <groupId>com.sankuai.jvm.sandbox</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mbop-sandbox-timeout-obtain</artifactId>
                        <groupId>com.meituan.mbop</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mapi-abtest-component</artifactId>
                <version>1.2.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mapi-log-util</artifactId>
                        <groupId>com.dianping.mobile</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>account-validation-api</artifactId>
                        <groupId>com.dianping</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hawk-client</artifactId>
                        <groupId>com.dianping.hawk</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>xstream</artifactId>
                        <groupId>com.thoughtworks.xstream</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mobile-api-base</artifactId>
                        <groupId>com.dianping.mobile</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>appkit-client</artifactId>
                        <groupId>com.dianping.appkit</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cat-core</artifactId>
                        <groupId>com.dianping.cat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-mtshoplist-api</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-shop-api</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-bcp-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-trade-sale-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-detail-api</artifactId>
                <version>2.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>baby-operation-api</artifactId>
                <version>0.0.2.68</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.merchantcard</groupId>
                <artifactId>timescard-exposure-api</artifactId>
                <version>0.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-distance-common</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-acl-api</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-privilege-api</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mobile-base-datatypes</artifactId>
                <version>0.3.8</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mobile-api-base</artifactId>
                        <groupId>com.dianping.mobile</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-gis-api</artifactId>
                <version>0.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-search-api</artifactId>
                <version>0.0.13</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.campaign</groupId>
                <artifactId>proxy-thrift</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mms-boot</artifactId>
                        <groupId>com.sankuai.mms</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtthrift</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-web</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtrace</artifactId>
                        <groupId>com.meituan.mtrace</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.midas</groupId>
                <artifactId>baymax-ad-bizer-api</artifactId>
                <version>1.6.10</version>
                <exclusions>
                    <exclusion>
                        <artifactId>platform-sdk</artifactId>
                        <groupId>com.dianping.platform</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cat-core</artifactId>
                        <groupId>com.dianping.cat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.midas</groupId>
                <artifactId>baymax-mt-bizer-api-pigeon</artifactId>
                <version>0.0.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>platform-sdk</artifactId>
                        <groupId>com.dianping.platform</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cat-core</artifactId>
                        <groupId>com.dianping.cat</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dp.search</groupId>
                <artifactId>search-tohome-interface</artifactId>
                <version>0.1.21</version>
            </dependency>
            <dependency>
                <groupId>com.graphql-java</groupId>
                <artifactId>graphql-java</artifactId>
                <version>13.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi.flow</groupId>
                <artifactId>poi-main-api</artifactId>
                <version>0.2.19</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-rule-api</artifactId>
                <version>0.1.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-display-api</artifactId>
                <version>0.3.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>javassist</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-stock-query-api</artifactId>
                <version>2.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-stock-display-api</artifactId>
                <version>2.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-base-api</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-privilege-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-attribute-api</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-cateproperty-api</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.merchant</groupId>
                <artifactId>merchant-product-common-api</artifactId>
                <version>0.0.28</version>
                <exclusions>
                    <exclusion>
                        <artifactId>data-api</artifactId>
                        <groupId>com.dianping.dp</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.maoyan.mtrace</groupId>
                <artifactId>mtrace-http</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-common</artifactId>
                <version>1.1.56</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pay-common</artifactId>
                <version>1.2.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.prometheus</groupId>
                <artifactId>deal-client</artifactId>
                <version>1.6.1.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>org.springframework.jms</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-asm</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-webmvc</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-sale-api</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-common-util</artifactId>
                <version>1.0.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-voucher-query-api</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nibmktproxy</groupId>
                <artifactId>query-client</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-idmapper-api</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>geoinfo-api</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>easylife-remote</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.dataapp.search.dealtag</groupId>
                <artifactId>searchdealtag-client</artifactId>
                <version>0.1.5</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.message</groupId>
                <artifactId>grouppoi</artifactId>
                <version>0.1.35</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.stid</groupId>
                <artifactId>stid</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-display-octo-api</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>customerinfo-api</artifactId>
                <version>2.0.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>aqc-license-api</artifactId>
                <version>1.9.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.merchant</groupId>
                <artifactId>merchant-account-api</artifactId>
                <version>0.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.poi</groupId>
                <artifactId>sinai.client</artifactId>
                <version>3.1.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>scribe-log4j</artifactId>
                        <groupId>com.meituan.scribe</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>scala-library</artifactId>
                        <groupId>org.scala-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>junglepoi.client</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>junglepoi.client</artifactId>
                <version>1.0.8</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.group</groupId>
                <artifactId>groupbase</artifactId>
                <version>0.7.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>search-adx-api</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-shoplist-api</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-trade-platform-api</artifactId>
                <version>1.0.28</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-feature-api</artifactId>
                <version>0.1.4</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>6.14.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-sales-common</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-interface</artifactId>
                <version>0.0.32</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>communitylife-api</artifactId>
                <version>0.8.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-relation-service-api</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-info-api</artifactId>
                <version>1.0.12</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.dealuser</groupId>
                <artifactId>price-display-api</artifactId>
                <version>0.0.181</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nib.mkt</groupId>
                <artifactId>promotion-api</artifactId>
                <version>1.7.57</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-outer-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>merchant-member-api</artifactId>
                <version>2.0.20</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-data-api</artifactId>
                <version>2.2.3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>poi-shopcateprop-api</artifactId>
                        <groupId>com.dianping.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-operate-logger</artifactId>
                <version>0.0.26</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-merchant-activity-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-logic-api</artifactId>
                <version>1.2.8</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-marketing-nr</artifactId>
                <version>0.0.18</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-mq-client</artifactId>
                <version>0.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-plan-client</artifactId>
                <version>0.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-change-spi</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-volume-query-api</artifactId>
                <version>2.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-pagoda-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tgc</groupId>
                <artifactId>tgc-base-remote</artifactId>
                <version>0.1.51</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spurs-common</artifactId>
                        <groupId>com.dianping.spurs</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hawk-client</artifactId>
                        <groupId>com.dianping.hawk</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-style-spi</artifactId>
                <version>1.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-testing-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.joynav</groupId>
                <artifactId>joynav-navigation-api</artifactId>
                <version>1.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-bom</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-common</artifactId>
                <version>1.1.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-poi-nr</artifactId>
                <version>0.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>dztheme-deal-api</artifactId>
                <version>1.1.15</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-tag-query-api</artifactId>
                <version>1.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-tag-common</artifactId>
                <version>1.0.23</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.cache</groupId>
                <artifactId>redis-cluster-client</artifactId>
                <version>2.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gmm-investment-data-tool-api</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-client</artifactId>
                <version>0.0.46</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-sdk</artifactId>
                <version>1.11.47</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>org.mortbay.jetty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-operate-sdk</artifactId>
                <version>0.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-common-validation</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>struts2-core</artifactId>
                        <groupId>org.apache.struts</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>avatar-dao</artifactId>
                        <groupId>com.dianping</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-test</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>platform-sdk</artifactId>
                        <groupId>com.dianping.platform</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-utils</artifactId>
                <version>1.7.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>platform-sdk</artifactId>
                        <groupId>com.dianping.platform</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjlib</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pigeon-octo</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-client</artifactId>
                <version>0.5.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-consumerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-producerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <!-- 商户主题 -->
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dztheme-shop-api</artifactId>
                <version>0.0.145</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctpoiext</groupId>
                <artifactId>mpmctpoiext-info-query-thrift</artifactId>
                <version>1.0.25</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>review-api</artifactId>
                <version>5.7.3</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.swan.udqs</groupId>
                <artifactId>Swan-udqs-api</artifactId>
                <version>1.5.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tuangou</groupId>
                <artifactId>dztg-usercenter-api</artifactId>
                <version>2.2.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.general.product</groupId>
                <artifactId>client</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.feitianplus.data.onedata</groupId>
                        <artifactId>data-onedata-integration</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.0.46</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.spt</groupId>
                <artifactId>spu-common</artifactId>
                <version>1.0.23</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.sinai</groupId>
                <artifactId>sinai-api</artifactId>
                <version>1.0.71</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>technician-vc-api</artifactId>
                <version>1.15.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>mafka-client_2.9</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>mafka-client_2.10</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mapi-log-util</artifactId>
                <version>${mapi-log-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.wpt.user.merge</groupId>
                <artifactId>user-merge-query-api</artifactId>
                <version>1.0.1</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.powermock</groupId>-->
<!--                <artifactId>powermock-module-junit4</artifactId>-->
<!--                <version>2.0.9</version>-->
<!--                <scope>test</scope>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.powermock</groupId>-->
<!--                <artifactId>powermock-api-mockito2</artifactId>-->
<!--                <version>2.0.9</version>-->
<!--                <scope>test</scope>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>mockito-core</artifactId>-->
<!--                        <groupId>org.mockito</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>beautycontent.creator.api</artifactId>
                <version>0.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nibmp.dzbp</groupId>
                <artifactId>merchant-portal-api</artifactId>
                <version>0.4.0</version>
            </dependency>
            <!-- 神会员点位上报 -->
            <dependency>
                <groupId>com.sankuai.nib.mkt</groupId>
                <artifactId>magic-member-degrade</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nibpt</groupId>
                <artifactId>nibpt-transparent-validator</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.technician</groupId>
                <artifactId>technician-biz-api</artifactId>
                <version>${technician-biz-api.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-changelog-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <workspace>${project.basedir}/dztheme-dealgroup-service</workspace>
                        <workspaceCodeStylesURL>
                            http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-eclipse-plugin/src/optional/eclipse-config/maven-styles.xml
                        </workspaceCodeStylesURL>
                        <downloadSources>true</downloadSources>
                        <downloadJavadocs>false</downloadJavadocs>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.12.2</version>
                    <configuration>
                        <showSuccess>false</showSuccess>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.societegenerale.commons</groupId>
                    <artifactId>arch-unit-maven-plugin</artifactId>
                    <version>2.3.0</version>
                    <executions>
                        <execution>
                            <phase>test</phase>
                            <goals>
                                <goal>arch-test</goal>
                            </goals>
                        </execution>
                    </executions>
                    <dependencies>
                        <dependency>
                            <groupId>com.sankuai</groupId>
                            <artifactId>athena-arch-rules</artifactId>
                            <version>0.0.4</version>
                            <scope>compile</scope>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <projectPath>${project.basedir}/dztheme-dealgroup-service/target</projectPath>
                        <rules>
                            <configurableRules>
                                <configurableRule>
                                    <rule>com.sankuai.athena.arch.ArchRules</rule>
                                    <applyOn>
                                        <packageName>com.sankuai.dealtheme</packageName>
                                        <scope>main</scope>
                                    </applyOn>
                                    <checks>
                                        <check>CYCLOMATIC_COMPLEXITY_5_CHECKER</check>
                                        <check>LAYER_CHECKER</check>
                                    </checks>
                                </configurableRule>
                            </configurableRules>
                        </rules>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
