<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai</groupId>
        <artifactId>dztheme-dealgroup-home</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dztheme-dealgroup-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>dztheme-dealgroup-service</name>
    <properties>
        <env>qa</env>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-dealgroup-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <!--玩法中心-->
        <dependency>
            <groupId>com.sankuai.mktplay</groupId>
            <artifactId>mkt-play-center-client</artifactId>
        </dependency>
        <!--休娱-->
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-category-process-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.insurance.adapter</groupId>
            <artifactId>insurance-adapter-dc-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dzrank-scenes-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>joy-general-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-base-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-resource-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>mpproduct-publish-common</artifactId>
            <groupId>com.sankuai.mpproduct.publish</groupId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-produce-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctcontent</groupId>
            <artifactId>mpmctcontent-query-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-reception-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.lingtai</groupId>
            <artifactId>com.sankuai.lingtai.tag.api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.joynav</groupId>
            <artifactId>joynav-rb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-activity-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dpsf-net</artifactId>
                    <groupId>com.dianping.dpsf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-datapools-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>promotion-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-detail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>baby-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.general</groupId>
            <artifactId>martgeneral-recommend-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-deal-shop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-deal-acl-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-deal-privilege-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dentistry-book-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-deal-search-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-deal-change-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzaggregation</groupId>
            <artifactId>dzaggregation-platformpoilist-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-sdk-shopuuid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.hsr</groupId>
            <artifactId>geohash</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dp.search</groupId>
            <artifactId>mainshop-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-tagsearch-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-process-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-common</artifactId>
            <version>0.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-query-thrift</artifactId>
            <version>0.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-underlayer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-client</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>graphql-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cat-client</artifactId>
                    <groupId>com.dianping.cat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-ugc-nr</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gateway-framework-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ops-remote-host</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-common-filter</artifactId>
                    <groupId>com.dianping.merchant</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-member-card-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-product-nr</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.deal</groupId>
                    <artifactId>deal-shop-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>product-shelf-query-api</artifactId>
                    <groupId>com.dianping.deal</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-abtest-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-mtshoplist-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-bcp-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-trade-sale-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>timescard-exposure-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-distance-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-voucher-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmktproxy</groupId>
            <artifactId>query-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mobile-base-datatypes</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>freemarker</artifactId>
                    <groupId>org.freemarker</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-rule-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-gis-api</artifactId>
            <version>0.4.17</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.campaign</groupId>
            <artifactId>proxy-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-meta-tag-manage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.midas</groupId>
            <artifactId>baymax-ad-bizer-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swallow-common</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.midas</groupId>
            <artifactId>baymax-mt-bizer-api-pigeon</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swallow-common</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dp.search</groupId>
            <artifactId>search-tohome-interface</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi.flow</groupId>
            <artifactId>poi-main-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-stock-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-stock-display-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-privilege-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-attribute-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-cateproperty-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-product-common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.maoyan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.prometheus</groupId>
            <artifactId>deal-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sale-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-idmapper-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>geoinfo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>easylife-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.dataapp.search.dealtag</groupId>
            <artifactId>searchdealtag-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.message</groupId>
            <artifactId>grouppoi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.stid</groupId>
            <artifactId>stid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-octo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>customerinfo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>aqc-license-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.poi</groupId>
            <artifactId>sinai.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>junglepoi.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.group</groupId>
            <artifactId>groupbase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>search-adx-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-shoplist-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-trade-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-feature-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-interface</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>communitylife-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-info-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-outer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>merchant-member-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-data-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-operate-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-merchant-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-logic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-marketing-nr</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swallow-common</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-mq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-plan-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-volume-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-pagoda-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-base-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-testing-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.joynav</groupId>
            <artifactId>joynav-navigation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-bom</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-poi-nr</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-shop-api</artifactId>
            <version>1.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>appkit-client</artifactId>
                    <groupId>com.dianping.appkit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.appkit</groupId>
            <artifactId>appkit-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lion-client</artifactId>
                    <groupId>com.dianping.lion</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.cache</groupId>
            <artifactId>redis-cluster-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gmm-investment-data-tool-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-async-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-operate-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>vc-meta-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-common-validation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ops-remote-host</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-utils</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ops-remote-host</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- mockito-inline -->
        <!-- Mockito 3.7.7 unified order of verify parameters (Issue #2173) -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.reactivex.rxjava2</groupId>
            <artifactId>rxjava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-metrics-event-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair3-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-tracker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pigeon-octo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-consumerclient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swallow-common</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-producerclient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swallow-common</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.dianping.swallow</groupId>-->
<!--            <artifactId>swallow-common</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>sec-sdk</artifactId>
                    <groupId>com.sankuai.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ops-remote-host</artifactId>
            <version>1.1.0-IPV6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-rgc-api</artifactId>
            <version>0.1.20</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.18</version>
        </dependency>
        <!-- 团单商户推荐 -->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
            <version>1.9.8</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-publish-category-api</artifactId>
            <version>1.1.5</version>
        </dependency>

        <!--主题能力-->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-framework-themeclient</artifactId>
            <version>0.3.8.15</version>
        </dependency>

        <!--团单类目-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tuangou-category-api</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 商户主题 -->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctpoiext</groupId>
            <artifactId>mpmctpoiext-info-query-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>review-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.swan.udqs</groupId>
            <artifactId>Swan-udqs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dztg-usercenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
            <version>2.10.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.feitianplus.data.onedata</groupId>
                    <artifactId>data-onedata-integration</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spu-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fbi</groupId>
            <artifactId>faas-wed-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.62</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-common</artifactId>
            <version>0.0.18</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpmctstock</groupId>
            <artifactId>mpmctstock-core-common</artifactId>
            <version>0.0.20</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-count-thrift</artifactId>
            <version>0.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibtp</groupId>
            <artifactId>trade-client</artifactId>
            <version>1.9.92</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.price.operation</groupId>
            <artifactId>price-operation-api</artifactId>
            <version>1.0.22</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mppack.product</groupId>
            <artifactId>mppack-api-client</artifactId>
            <version>1.0.85</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-display-api</artifactId>
            <version>2.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-common-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-union-log</artifactId>
            <version>0.0.2</version>
        </dependency>
        <!-- 手艺人信息-->
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-common-api</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
            <version>0.4.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.live</groupId>
            <artifactId>live-biz-api</artifactId>
            <version>1.1.1.129</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-log-util</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.general</groupId>
            <artifactId>order-query-center-api</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.powermock</groupId>-->
<!--            <artifactId>powermock-module-junit4</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.powermock</groupId>-->
<!--            <artifactId>powermock-api-mockito2</artifactId>-->
<!--            <scope>test</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>mockito-core</artifactId>-->
<!--                    <groupId>org.mockito</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctcontent</groupId>
            <artifactId>mpmctcontent-application-thrift</artifactId>
            <version>0.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>beautycontent.creator.api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmp.dzbp</groupId>
            <artifactId>merchant-portal-api</artifactId>
        </dependency>
        <!-- 神会员点位上报 -->
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>magic-member-degrade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-transparent-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.lifeevent</groupId>
            <artifactId>reserve-rpc-api</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-standardproduct-api</artifactId>
            <version>0.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-api</artifactId>
            <version>1.1.15</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fbi</groupId>
            <artifactId>merchant-miniprogram-registration-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!--家政洗涤预约时间查询-->
        <dependency>
            <groupId>com.sankuai.lifeevent</groupId>
            <artifactId>communitylife-dealgroup-channel-openapi</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!--飞天平台-->
        <dependency>
            <groupId>com.sankuai.feitianplus.data.onedata</groupId>
            <artifactId>data-onedata-api</artifactId>
            <version>1.0.37</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>com/sankuai/dealtheme/core/**</include> <!-- 指定要包含的路径 -->
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.societegenerale.commons</groupId>
                <artifactId>arch-unit-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.0.7.RELEASE</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <layoutFactory implementation="com.sankuai.athena.boot.loader.AthenaLayoutFactory"></layoutFactory>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>athena-home</groupId>
                                <artifactId>athena-boot-loader</artifactId>
                                <version>1.0.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>


