package com.sankuai.dealtheme.core.nr.atom;

import com.dianping.account.dto.UserAccountDTO;
import com.dianping.aqc.license.enums.LicenseCategoryEnum;
import com.dianping.baby.timeLimitTg.dto.BabyHotSaleTgDTO;
import com.dianping.beauty.deal.biz.dto.BeautyTagMap;
import com.dianping.beauty.tag.bean.BeautyTagItem;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.detail.dto.*;
import com.dianping.deal.sale.api.dto.ProductSaleDto;
import com.dianping.deal.sale.api.dto.QueryProductSaleRequest;
import com.dianping.deal.sales.common.datatype.*;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.deal.tag.dto.*;
import com.dianping.deal.voucher.query.api.dto.VoucherDTO;
import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gm.bonus.exposure.api.dto.ConsumeBonusSimpleListDTO;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityResponse;
import com.dianping.gmkt.activity.api.request.CheckDealActivityRelationRequest;
import com.dianping.gmkt.activity.api.request.CheckDealItemRelationRequest;
import com.dianping.gmkt.activity.api.request.QueryActivityRequest;
import com.dianping.gmkt.activity.api.response.CheckDealActivityRelationResponse;
import com.dianping.gmkt.activity.api.response.CheckDealItemRelationResponse;
import com.dianping.gmkt.activity.api.response.QueryActivityResponse;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.joy.category.process.api.dto.bar.barProductManage.GetUrlReqDTO;
import com.dianping.joy.category.process.api.dto.bar.barProductManage.GetUrlRespDTO;
import com.dianping.joygeneral.api.insurance.dto.ReadInsuranceReqDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsQueryDTO;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.dto.activity.ActivityShelfToCDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTOList;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.common.request.activities.ProductQueryByShopIdToCRequest;
import com.dianping.product.shelf.common.request.subjectManage.SubjectConfigQueryRequest;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.technician.biz.dto.TechnicianDetailDTO;
import com.dianping.tpfun.product.api.category.model.ProductCategory;
import com.dianping.tpfun.product.api.sku.aggregate.dto.ProductUrlDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductUrlRequest;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.platform.resource.dto.ResourceInfoDTO;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.tpfun.product.api.sku.request.GetProductItemRequest;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.dianping.userremote.base.dto.UserDTO;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.beautycontent.security.displaycontrol.request.DisplayControlBatchDealRequest;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlBatchDealResponse;
import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryLatestStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryPlanListReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryResvBookingTagReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.QueryStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryLatestStockRespDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryResvBookingTagRespDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.QueryStockRespDTO;
import com.sankuai.dealtheme.core.fetchers.model.DealCustomStructModel;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.fbi.faas.wed.dto.PictureBatchQueryReqDTO;
import com.sankuai.fbi.faas.wed.dto.PictureRespDTO;
import com.sankuai.fbi.faas.wed.dto.ServiceResponse;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.WashingWhiteCustomerListRequest;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.BatchMiniprogramJumpLinkRequest;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.MiniprogramJumpLinkDTO;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.insurance.adapter.dc.base.SignInfo;
import com.sankuai.insurance.adapter.dc.service.search.ylx.YlxPackageSearchRequest;
import com.sankuai.insurance.adapter.dc.service.search.ylx.YlxPackageSearchResp;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.count.thrift.dto.req.QueryLeadsSalesReqDTO;
import com.sankuai.leads.count.thrift.dto.resp.QueryLeadsSalesRespDTO;
import com.sankuai.lifeevent.communitylife.dealgroup.channel.api.dto.reserve.DealGroupEarliestReserveTimeReq;
import com.sankuai.lifeevent.communitylife.dealgroup.channel.api.dto.responses.DealGroupEarliestReserveTimeResp;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSimpleDTO;
import com.sankuai.merchantcard.timescard.exposure.req.QueryCardSimpInfoRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadContentByExternalBizIdDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.TagValueDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.QueryContentDetailReqDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.QueryContentDetailRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedExhibitsReq;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedExhibitsResp;
import com.sankuai.mpmctexhibit.process.request.GetRelateExhibitInfoListReq;
import com.sankuai.mpmctexhibit.process.response.GetRelateExhibitInfoListResp;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.BatchGetGrouponMemberDiscountInfoReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.BatchVerifyProductMemberExclusiveReqDTO;
import com.sankuai.mpmctpoiext.info.query.thrift.biz.car.dto.RentalCarDepositRuleDTO;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionRequest;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.integration.IntegrationParams;
import com.sankuai.technician.trade.api.product.dto.TechProductUrlMultiDTO;
import com.sankuai.technician.trade.api.product.request.TechProductUrlBatchQuery;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.zdc.produce.dto.ShopServiceRegionDTO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Created by zhaizhui on 2020/8/17
 */
public interface FacadeService {

    /**
     * 获取多个shopUuid
     * @param shopIds
     * @return
     */
    CompletableFuture<List<DpPoiDTO>> multiShopUuid(List<Long> shopIds, Execution execution);

    /**
     * 批量查询shopuuid
     * @param dpShopIds 点评门店id
     * @return shop uuid
     */
    CompletableFuture<List<DpPoiDTO>> batchQueryShopUUid(List<Long> dpShopIds);

    /**
     * 批量获取推荐信息
     *
     * @param bizTypeId
     * @param queryKey
     * @param params
     * @return
     */
    CompletableFuture<List<Map<String, Object>>> batchQueryRecommendInfo(int bizTypeId, String queryKey, IntegrationParams params);

    /**
     * swan接口调用
     * 查钩子单
     * @param bizTypeId bizTypeId
     * @param queryKey queryKey
     * @param params params
     * @return List
     */
    CompletableFuture<List<Map<String, Object>>> batchQuerySwanProduct(int bizTypeId, String queryKey, SwanParam params);

    /**
     * 批量获取团单的可用时间列表（包括不可用时间）
     *
     * @param dpDealGroupIds
     * @return
     */
    CompletableFuture<Map<Integer, DealGroupAvailableDateDTO>> batchGetProductAvailableDates(List<Integer> dpDealGroupIds);

    /**
     * 根据点评用户ID获取点评用户信息
     *
     * @param dpUserIds 最多50个
     * @return
     */
    CompletableFuture<Map<Long, UserDTO>> batchGetUserMap(List<Long> dpUserIds);

    /**
     * 根据美团用户ID获取美团用户信息
     *
     * @param mtUserIds 最多30个
     * @param fields
     * @return
     */
    CompletableFuture<List<UserModel>> batchQueryUserInfoByMtUserIds(List<Long> mtUserIds, UserFields fields);

    /**
     * 根据商品ID获取下单商品、用户等信息
     *
     * @param request 商品ID为点评商品ID
     * @return
     */
    CompletableFuture<List<ProductSaleDto>> batchQueryCompleteProductSale(QueryProductSaleRequest request);

    /**
     * 根据团单ID列表、平台、城市ID、商户ID判断商户下的团单是否可展示
     *
     * @param displayControlBatchDealRequest
     * @return
     */
    CompletableFuture<DisplayControlBatchDealResponse> batchGetDisplayControlStatus(DisplayControlBatchDealRequest displayControlBatchDealRequest);

    /**
     * 获取商品关联款式等的数量
     *
     * @param req
     * @return
     */
    CompletableFuture<GetBindedExhibitsResp> getBindedExhibitCount(GetBindedExhibitsReq req);

    /**
     * 批量查询团单详情页信息
     */
    CompletableFuture<DealGroupAttributeDTO> getSingleDealDetailText(int dealGroupIds);

    /**
     * 查询消费返券信息
     *
     * @param dealGroupIds 点评团单信息
     * @return
     */
    CompletableFuture<Map<Integer, ConsumeBonusSimpleListDTO>> batchGetConsumeBonusInfoByDealGroupIds(List<Integer> dealGroupIds);


    /**
     * 查询业务侧维护的结构化信息，目前用于医疗货架
     *
     * @param dealGroupIds
     * @param configId
     * @return
     */
    CompletableFuture<Map<Integer, DealCustomStructModel>> queryStructData(List<Integer> dealGroupIds, int configId);

    /**
     * 批量查询美团shopId对应的点评shopIds
     *
     * @param mtShopIds
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> bathGetDpShopIdsByMtIdsL(List<Long> mtShopIds);

    /**
     * 批量查询点评shopId对应的美团shopIds
     *
     * @param
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> queryMtShopIdByDpIdsLong(List<Long> dpShopIds);

    /**
     * 查询团单的玩乐卡、折扣卡、会员日信息
     *
     * @param request
     * @return
     */
    CompletableFuture<PriceResponse<Map<Integer, List<PriceDisplayDTO>>>> batchQueryPrice(BatchPriceRequest request);

    /**
     * 批量查询商品价格，主要用于列表页、货架
     * poiid升级后的接口
     *
     * @param request
     * @return
     */
    CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> batchQueryPriceByLongShopId(BatchPriceRequest request);

    /**
     * 批量查询交易场景下的团单价格，主要用于购物车场景打开查价比价浮层
     * @param request 价格Id、用户Id、场景信息、请求环境信息
     * @return 交易价格
     */
    CompletableFuture<PriceResponse<BatchTradePriceResponse>> batchQueryPriceForTrade(BatchTradePriceRequest request);


    /**
     * 获取团单的可用时间展示文案
     *
     * @param request 请求参数，dealId只支持点评
     * @return
     */
    CompletableFuture<Map<Integer, DealGroupAvailableDateShowTextDTO>> batchGetDealGroupAvailableDateShowText(AvailableDateShowTextRequest request);


    /**
     * 获取团单的可用时间展示文案
     *
     * @param dealGroupIDs 请求参数，dealId只支持点评
     * @return
     */
    CompletableFuture<Map<Integer, DealGroupAvailableDateDTO>> batchGetDealGroupAvailableDatesWithDefault(List<Integer> dealGroupIDs, boolean needDisableDate);


    /**
     * 获取丽人商品的主标签
     *
     * @param dpDealIds 点评商品ids
     * @param goodType  商品类型 @see GoodsType
     * @return
     */
    CompletableFuture<Map<Integer, String>> multiLoadBeautyMainDealTag(List<Integer> dpDealIds, int goodType);

    /**
     * 丽人商品所有标签批量查询方法
     *
     * @param dpProductIds 点评商品ids
     * @param goodType     商品类型 @see GoodsType
     * @return
     */
    CompletableFuture<Map<Integer, List<BeautyTagItem>>> multiLoadAllBeautyDealTag(List<Integer> dpProductIds, int goodType);

    /**
     * 丽人特价团购团单标签批量查询方法
     *
     * @param dpProductIds 点评商品ids
     * @param goodType     商品类型 @see GoodsType
     * @return
     */
    CompletableFuture<Map<Integer, String>> batchQueryBeautySpecialPromotionTag(List<Integer> dpProductIds, int goodType);


    /**
     * 查询团单销量
     *
     * @param salesDisplayRequest
     * @return
     */
    CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetDealSales(SalesDisplayRequest salesDisplayRequest);

    /**
     * 查询团单销量（商品维度）
     *
     * @param salesDisplayRequest
     * @return
     */
    CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetProductSceneSales(SalesDisplayRequest salesDisplayRequest);

    /**
     * 查询团单销量（商品维度）
     *
     * @param salesDisplayRequest
     * @return
     */
    CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetProductSceneSales(SalesDisplayRequest salesDisplayRequest, Execution execution);

    CompletableFuture<Map<SubjectParam, SubjectSalesDisplayDTO>> multiGetSubjectSales(SubjectSalesDisplayRequest request);

    CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> batchQuerySales(SalesDisplayQueryRequest request);

    /**
     * 获取团单关联案例列表
     *
     * @param req
     * @return
     */
    CompletableFuture<GetRelateExhibitInfoListResp> getRelatedExhibits(GetRelateExhibitInfoListReq req);

    CompletableFuture<Map<Integer, ProductSaleDto>> multiQueryProductSaleForLong(Set<Integer> productGroupIds, int source, long dpShopId);

    /**
     * 查询彩虹活动
     *
     * @param request
     * @return
     */
    CompletableFuture<BatchQueryDealActivityResponse> batchQueryDealActivity(BatchQueryDealActivityRequest request);

    /**
     * 根据类目查询曝光信息
     *
     * @param request
     * @return
     */
    CompletableFuture<QueryActivityResponse> getPageByCategory(QueryActivityRequest request);

    /**
     * 判断团单ids是否在对应楼层中
     *
     * @param request
     * @return
     */
    CompletableFuture<CheckDealItemRelationResponse> checkDealItemRelation(CheckDealItemRelationRequest request);

    /**
     * 查询所有标签Map(仅供内部使用)
     */
    CompletableFuture<List<BeautyTagMap>> loadAll();

    /**
     * 根据productCategoryId批量查询对应的中文名称
     */
    CompletableFuture<Map<Long, ProductCategory>> getProductCategoryList(List<Long> productCategoryIds);

    /**
     * 根据服务项目名称查询网友推荐
     */
    CompletableFuture<GetUrlRespDTO> batchGetShopRecommendInfo(GetUrlReqDTO getUrlReqDTO);

    /**
     * 批量查询优惠立减
     */
    CompletableFuture<Response<Map<Product, List<PromoDisplayDTO>>>> batchQueryPromoDisplay(BatchQueryPromoDisplayRequest request);

    /**
     * 获取海马信息
     *
     * @param haimaRequest
     * @return
     */
    CompletableFuture<HaimaResponse> getHaimaResponse(HaimaRequest haimaRequest);

    /**
     * 获取门店提供服务的地区
     *
     * @param shopId
     * @return
     */
    CompletableFuture<ShopServiceRegionDTO> queryShopRegion(long shopId);

    /**
     * 批量查询团购结算凭证
     */
    CompletableFuture<Map<Integer, List<VoucherDTO>>> batchQueryDealGroupVoucher(List<Integer> dpDealGroupIDs);

    /**
     * 根据点评userId查询用户信息
     */
    CompletableFuture<UserAccountDTO> loadUserByDpId(long dpUserId);

    /**
     * 根据美团userId查询用户信息
     */
    CompletableFuture<UserModel> getUserByMtUserId(long userId, UserFields fields);

    /**
     * 查询优惠
     */
    CompletableFuture<PromotionResponse> getPromotions(PromotionRequest promotionRequest);

    /**
     * 根据美团城市ID获取点评城市ID
     *
     * @param mtCityId
     * @return
     */
    CompletableFuture<Integer> getDpCityIdByMtCityId(int mtCityId);

    /**
     * 根据标签id集查询多个标签
     *
     * @param tagIds tagId集
     * @return 标签集
     */
    CompletableFuture<List<MetaTagDTO>> queryByTagIds(List<Long> tagIds);

    /**
     * 判断团单id是否处于活动中
     *
     * @param request
     * @return
     */
    CompletableFuture<CheckDealActivityRelationResponse> checkDealActivityRelation(CheckDealActivityRelationRequest request);

    /**
     * 查询次卡
     */
    CompletableFuture<CardResponse<Map<Integer, CardSimpleDTO>>> queryCardSimpleInfoByDealIds(QueryCardSimpInfoRequest request);

    /**
     * 查询最近适用门店
     *
     * @param req
     * @return
     */
    CompletableFuture<BestShopDTO> getDealGroupBestShop(BestShopReq req);


    /**
     * 批量查询城市信息
     *
     * @param cityIds
     * @return
     */
    CompletableFuture<List<CityInfoDTO>> findCities(List<Integer> cityIds);

    CompletableFuture<Map<Long, ShopCardDTO>> multiGetShopThemeDtoForLong(ShopThemePlanRequest request);

    CompletableFuture<Map<Long, ShopCardDTO>> multiGetShopThemeDtoForLong(ShopThemePlanRequest request, Execution execution);

    /**
     * 根据团单id批量查询团单详情
     *
     * @param dealGroupIds
     * @return
     */
    CompletableFuture<Map<Integer, DealGroupDetailDTO>> multiGetDealGroupDetails(List<Integer> dealGroupIds);

    /**
     * 根据点评cityId、点评团购id查询线爆款特卖区团单
     *
     * @param dpCityId
     * @param dpDealId
     * @return
     */
    CompletableFuture<BabyHotSaleTgDTO> getOnLineHotSaleByCityIdAndDealId(int dpCityId, int dpDealId);

    /**
     * 查询团单累计销量
     *
     * @param keyParams
     * @param productType
     * @param bizType
     * @param dimension
     * @return
     */
    CompletableFuture<Map<KeyParam, SpuSale>> multiGetBySpuProduct(List<KeyParam> keyParams, int productType, int bizType, int dimension);

    /**
     * 查询团单销量
     */
    CompletableFuture<Map<KeyParam, SpuSale>> multiGetBySpuProductCycle(List<KeyParam> keyParams, int productType, int bizType, int dimension);

    /**
     * 根据美团shopIdL查询poi信息
     */
    CompletableFuture<Map<Long, PoiModelL>> listPoisL(List<Long> mtShopIds, List<String> fields);

    /**
     * 批量查询团购绑定的主题
     *
     * @param req
     * @return
     */
    CompletableFuture<GetBindedExhibitsResp> getBindedExhibits(GetBindedExhibitsReq req);

    /**
     * 批量查询团购绑定主题（优化新接口）
     *
     * @param req
     * @return
     */
    CompletableFuture<com.sankuai.mpmctexhibit.query.response.GetBindedExhibitsResp> getFastBindedExhibits(com.sankuai.mpmctexhibit.query.request.GetBindedExhibitsReq req);

    /**
     * 查询租车门店押金规则
     *
     * @param mtShopId
     * @return
     */
    CompletableFuture<Pair<Long, RentalCarDepositRuleDTO>> queryRentCarShopDepositRule(Long mtShopId);

    /**
     * 团单关联款式信息
     *
     * @param dpDealGroupIds
     * @param contentType
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> queryContentIdsByDpDealGroupIdsAndContentType(List<Long> dpDealGroupIds, int contentType);

    /**
     * 团单关联款式信息查询
     *
     * @param reqDTO
     * @return
     */
    CompletableFuture<QueryContentDetailRespDTO> queryContentDetails(QueryContentDetailReqDTO reqDTO);

    /**
     * 获取点评的评价星级分布
     *
     * @param dpDealIds
     * @return
     */
    CompletableFuture<List<ReviewStarDistributionDTO>> getDpReviewStar(List<Integer> dpDealIds);

    /**
     * 获取美团的评价星级分布
     *
     * @param mtDealIds
     * @return
     */
    CompletableFuture<Map<Integer, ReviewCount>> getMtReviewStar(List<Integer> mtDealIds);

    /**
     * 获取团单是否有外景
     * WARN：一次20最多
     *
     * @param dpDealIds
     * @return
     */
    CompletableFuture<Map<Integer, Boolean>> getBabyDealHasOutdoorTheme(List<Integer> dpDealIds);

    /**
     * @param dpDealIds
     * @return 批量查询团单标签
     */
    CompletableFuture<Map<Long, List<MetaTagTreeByProductDTO>>> batchQueryProductTreeTag(List<Long> dpDealIds);


    /**
     * @param batchQueryResourceInfoRequest
     * @return 批量查询资源信息
     */
    CompletableFuture<List<ResourceInfoDTO>> batchQueryResourceInfo(BatchQueryResourceInfoRequest batchQueryResourceInfoRequest);

    /**
     * @param request
     * @param execution
     * @return 查询团单对应的提单页跳链。key-团单id，value-跳链
     */
    CompletableFuture<Map<String, String>> batchGetCreateOrderPageUrl(BatchGetCreateOrderPageUrlReq request, Execution execution);


    /**
     * @param request
     * @return 根据团购id查询团购商品信息，支持按模块，按需查询
     */
    CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds(QueryByDealGroupIdRequest request);

    CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds2(QueryByDealGroupIdRequest request);

    CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds3(QueryByDealGroupIdRequest request);

    /**
     * @param reqDTO
     * @return
     */
    /**
     * 获取团单推荐结果
     *
     * @param recommendParameters
     * @return
     */
    CompletableFuture<RecommendResult<RecommendDTO>> getDealRecommendResult(RecommendParameters recommendParameters);


    CompletableFuture<Map<Integer, Boolean>> batchVerifyProductMemberExclusive(BatchVerifyProductMemberExclusiveReqDTO reqDTO);


    CompletableFuture<Map<Integer, List<String>>> getDealSubTitle(DealProductRequest request);

    /**
     * 获取美团商户信息
     * @param request
     * @return
     */
    CompletableFuture<Map<Long, DpPoiDTO>> batchQueryDpPoiDTO(DpPoiRequest request);

    /**
     * @param shopIds
     * @return 根据商户ID查询门店类型
     */
    CompletableFuture<Map<Long, DpPoiDTO>> batchGetShopInfoByShopIds(List<Long> shopIds);


    /**
     * swan数据查询
     *@param
     *@return
     */
    CompletableFuture<List<Map<String, Object>>> querySwanDataResult(Integer bizTypeId, String queryKey, SwanParam swanParam);

    CompletableFuture<List<TechnicianDetailDTO>> queryRelateTech(int dpDealId);

    /**
     * 批量查询智能裁切图
     * @param pictureBatchQueryReqDTO
     * @return
     */
    CompletableFuture<ServiceResponse<Map<String, PictureRespDTO>>> batchQuerySmartHeadPic(PictureBatchQueryReqDTO pictureBatchQueryReqDTO);

    /**
     * 批量查询团购sku概要
     * @param batchRequest
     * @return
     */
    CompletableFuture<Map<Long, DealSkuSummaryDTO>> batchQuerySummary(SkuOptionBatchRequest batchRequest);


    CompletableFuture<RankingResult> queryDealFirstRank(RankLabelDataRequest rankLabelDataRequest);

    CompletableFuture<BatchQueryResvBookingTagRespDTO> batchQueryResvBookingTag(BatchQueryResvBookingTagReqDTO reqDTO);

    CompletableFuture<LeadsCountRespDTO> queryLeadsCount(LeadsCountADTO leadsCountADTO);
    CompletableFuture<QueryLeadsSalesRespDTO> queryLeadsSales(QueryLeadsSalesReqDTO reqDTO);
    /**
     * 批量查询价格保证信息
     * @param sessionContext
     * @param request
     * @return
     */
    CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryProductGuarantee(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request);

    CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryProductGuarantee(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request, Execution execution);

    /**
     * 根据团单对应业务客户ID，批量查询保险状态
     * @param reqDTOS
     * @return
     */
    CompletableFuture<Map<Long, Boolean>> batchCheckInsuranceAvailable(List<ReadInsuranceReqDTO> reqDTOS);

    /**
     * 查询游乐险
     * @param request
     * @param signInfo
     * @return
     */
    CompletableFuture<YlxPackageSearchResp> searchInsurance(YlxPackageSearchRequest request, SignInfo signInfo);

    /**
     * 批量查询商家会员商品会员价信息信息
     * @param request
     * @return
     */
    CompletableFuture<Map<Integer, MemberDiscountInfoDTO>> batchQueryMerchantMemberProducts(BatchGetGrouponMemberDiscountInfoReqDTO request);

    /**
     * 根据点评实id查找美团虚id
     * @param dpUserId
     * @return
     */
    CompletableFuture<Long> loadDpUserId2MtUserId(Long dpUserId);

    /**
     * 根据点评实id查找美团实id
     * @param dpUserId
     * @return
     */
    CompletableFuture<Long> getMtRealUserIdByDpUserId(long dpUserId);

    /**
     * 查询0元预约预约预约状态
     * @param queryStockReqDTO
     * @return
     */
    CompletableFuture<QueryStockRespDTO> queryResvStatus(QueryStockReqDTO queryStockReqDTO);

    CompletableFuture<BatchQueryResvBookingTagRespDTO> batchQueryResvBookingTagV1(BatchQueryResvBookingTagReqDTO reqDTO);

    /**
     * 查询款式信息
     * @param request
     * @return
     */
    CompletableFuture<LoadDealRelatedCaseListRespDTO> queryDealRelatedCase(LoadContentByExternalBizIdDTO request);

    /**查询团单到店礼
     * @param reqDTO
     * @return
     */
    CompletableFuture<List<PlanDTO>> batchQueryPlanList(BatchQueryPlanListReqDTO reqDTO);

    CompletableFuture<SearchDetailResponseDTO> searchDetail(String searchScene, List<Long> list);

    /**
     * 查询团单预订信息
     * @param latestStockReqDTO
     * @return
     */
    CompletableFuture<BatchQueryLatestStockRespDTO> batchQueryLatestStock(BatchQueryLatestStockReqDTO latestStockReqDTO);

    /**
     * 点评门店转美团门店
     * @param dpPoiId
     * @return
     */
    CompletableFuture<Long> getMtByDpPoiIdL(long dpPoiId);

    /**
     * 根据POI+GOODS查询活动信息
     * @param request
     * @return
     */
    CompletableFuture<PlayExecuteResponse> batchQueryDealScenePlay(ScenePlayExecuteRequest request);

    /**
     * 入参团单、标签ID -> 出参 该团单是否含有招牌套餐
     * 单次团单数量不超过50个，自带分批
     */
    CompletableFuture<SubjectTagBatchJudgeResponse> batchQueryDealSubjectTags(SubjectTagBatchJudgeRequest request);

    CompletableFuture<ActivityDetailDTO> queryOnlineActivity(ActivityProductQueryRequest request);

    CompletableFuture<ActivityShelfToCDTO> queryProductListByShopIdToC(ProductQueryByShopIdToCRequest request);

    CompletableFuture<SubjectConfigDTOList> subjectConfigQuery(SubjectConfigQueryRequest request);

    /**
     * 批量查询团单预约留资信息
     * @param request
     * @return
     */
    CompletableFuture<BatchQueryLeadsInfoRespDTO> batchQueryDealLeadsInfo(BatchQueryLeadsInfoReqDTO request);

    /**
     * 查询泛商品信息
     */
    CompletableFuture<List<com.dianping.tpfun.product.api.sku.model.Product>> mGetBaseProductByIds(List<Integer> productIds);

    /**
     * 查询泛商品skuid
     */
    CompletableFuture<Map<Integer, List<Integer>>> getItemIdsByProductIds(List<Integer> productIds);

    /**
     * 根据泛商品skuid查询泛商品信息
     */
    CompletableFuture<Map<Integer, ProductItem>> getItemsById(GetProductItemRequest var1);

    /**
     * 查询泛商品销量信息
     */
    CompletableFuture<List<SaleStatisticsDTO>> queryFunSaleStatistics(SaleStatisticsQueryDTO saleStatisticsQueryDTO);

    /**
     * 查询供给中心摘要接口
     * @param queryDigestRequestDTO
     * @return
     */
    CompletableFuture<QueryDigestResponseDTO> queryDigest(QueryDigestRequestDTO queryDigestRequestDTO);

    /**
     * 查询洗涤门店白名单
     * @param washingWhiteCustomerListRequest
     * @return
     */
    CompletableFuture<List<Long>> queryWashingWhiteCustomerList(WashingWhiteCustomerListRequest washingWhiteCustomerListRequest);

    /**
     * 批量查询门店证件审核通过状态
     * @param shopIds 门店ID列表
     * @param licenseCategoryEnum 证件类型
     * @return
     */
    CompletableFuture<Map<Long, Boolean>> batchQueryShopLicenseAuthSuccessStatus(List<Long> shopIds, LicenseCategoryEnum licenseCategoryEnum);

    CompletableFuture<TechProductUrlMultiDTO> queryTechProductUrl(TechProductUrlBatchQuery request);
    /**
     * 查询标签值元信息
     */
    CompletableFuture<Map<String, List<TagValueDTO>>> queryTagValueMetaInfo(int bizLine, String entityName);

    /**
     * 家政保洁清洗预约时间查询
     * @param request
     * @return
     */
    CompletableFuture<DealGroupEarliestReserveTimeResp> queryDealGroupEarliestReserveTimeInfo(DealGroupEarliestReserveTimeReq request);

    /**
     * 飞天平台接口查询
     * @param request
     * @return
     */
    CompletableFuture<QueryResultSync> queryByKey(KeyQueryParamSync request);

    /**
     * 美的家电货架接小程序-批量查询小程序跳链
     * @param request
     * @return
     */
    CompletableFuture<List<MiniprogramJumpLinkDTO>> batchGetMiniProgramJumpLink(BatchMiniprogramJumpLinkRequest request);

    /**
     * 查询泛商品跳链
     */
    CompletableFuture<Map<Integer, List<ProductUrlDTO>>> getProductUrls(GetProductUrlRequest getProductUrlRequest);

    CompletableFuture<Map<Long, List<MetaTagTreeByProductDTO>>> batchGetProductTreeTagFromCache(List<Long> productIds, int productType);

    /**
     * 根据美团用户ID获取点评用户ID
     * @param mtUserId 美团用户ID
     * @return 点评用户ID
     */
    CompletableFuture<Long> getRealBindByMtUserId(long mtUserId);

    /**
     * 搜索用户订单信息
     * @param userId 用户ID
     * @param platform 平台类型
     * @return 订单搜索结果
     */
    CompletableFuture<Map<String, Object>> searchOrder(long userId, int platform);

}
