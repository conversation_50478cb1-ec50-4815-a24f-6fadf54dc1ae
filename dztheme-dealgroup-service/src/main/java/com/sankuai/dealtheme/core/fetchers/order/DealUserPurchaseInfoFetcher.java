package com.sankuai.dealtheme.core.fetchers.order;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.graphql.ExecutionContext;
import com.sankuai.athena.graphql.FetchingContext;
import com.sankuai.athena.graphql.fetcher.BatchFetcher;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.dealtheme.DealThemeConfig;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.fetchers.utils.ContextUtils;
import com.sankuai.dealtheme.core.fetchers.utils.NodeUtils;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.general.order.querycenter.api.request.OrderSearchRequest;
import com.sankuai.general.order.querycenter.api.request.SessionContext;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 团单用户购买信息数据源
 * 统计用户在特定门店中购买指定商品的次数（180天内）
 */
@Fetcher(name = "DealUserPurchaseInfoFetcher",
        description = "团单用户购买信息数据源",
        type = "Deal",
        field = "dealUserPurchaseInfoModel",
        params = {
                DealThemeConfig.PLATFORM,
                DealThemeConfig.USER_ID,
                DealThemeConfig.DP_SHOP_ID_FOR_LONG,
                DealThemeConfig.MT_SHOP_ID_FOR_LONG
        },
        needFields = {})
public class DealUserPurchaseInfoFetcher extends BatchFetcher<Integer, DealUserPurchaseInfoModel> {
    private static final Logger LOGGER = LoggerFactory.getLogger(DealUserPurchaseInfoFetcher.class);
    
    @Value("${com.sankuai.dealtheme.core.fetchers.order.dealuserpurchaseinfofetcher.batch.size:200}")
    private int BATCH_SIZE = 200;
    
    @Value("${com.sankuai.dealtheme.core.fetchers.order.dealuserpurchaseinfofetcher.max.query.size:100}")
    private int MAX_QUERY_SIZE = 100;
    
    // 查询时间范围：180天
    private static final int QUERY_TIME_RANGE_DAYS = 180;

    @Override
    public int batchSize() {
        return BATCH_SIZE;
    }

    @Override
    public Integer batchKey(FetchingContext fetchingContext) {
        DealModel dealModel = fetchingContext.getSource();
        return dealModel.getDealId();
    }

    @Override
    public CompletableFuture<Map<Integer, DealUserPurchaseInfoModel>> batchGet(Map<Integer, FetchingContext> keyFetchContexts) {
        if (MapUtils.isEmpty(keyFetchContexts)) {
            LOGGER.info("DealUserPurchaseInfoFetcher batchGet with empty keyFetchContexts");
            return CompletableFuture.completedFuture(new HashMap<>());
        }

        // 获取执行上下文和平台参数
        Execution execution = ContextUtils.getExecution(keyFetchContexts);
        ExecutionContext executionContext = ContextUtils.getExecutionContext(keyFetchContexts);
        
        // 获取平台参数
        Integer platform = execution.getParameter(DealThemeConfig.PLATFORM);
        Long userId = execution.getParameter(DealThemeConfig.USER_ID);
        Long dpShopId = execution.getParameter(DealThemeConfig.DP_SHOP_ID_FOR_LONG);
        Long mtShopId = execution.getParameter(DealThemeConfig.MT_SHOP_ID_FOR_LONG);
        
        if (userId == null || userId <= 0) {
            LOGGER.info("DealUserPurchaseInfoFetcher batchGet with invalid userId: {}", userId);
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        
        // 获取点评团单ID
        CompletableFuture<List<Integer>> dpDealIdsFuture = getDpDealIds(keyFetchContexts);
        
        // 获取美团到点评的团单映射关系
        CompletableFuture<Map<Integer, Integer>> dp2mtDealIdMapFuture = NodeUtils.getDp2MTDealIdMap(executionContext);
        
        // 根据平台不同，获取对应的用户ID
        CompletableFuture<Long> dpUserIdFuture;
        if (platform != null && platform == 2) { // 美团平台
            FacadeService facadeService = AthenaBeanFactory.getBean(FacadeService.class);
            dpUserIdFuture = facadeService.getDpRealUserIdByMtUserId(userId)
                    .exceptionally(ex -> {
                        LOGGER.error("Failed to get dpUserId by mtUserId: {}, error: {}", userId, ex.getMessage(), ex);
                        return 0L;
                    });
        } else { // 点评平台
            dpUserIdFuture = CompletableFuture.completedFuture(userId);
        }
        
        // 组合多个Future，查询订单信息
        return CompletableFuture.allOf(dpDealIdsFuture, dp2mtDealIdMapFuture, dpUserIdFuture)
                .thenCompose(v -> {
                    List<Integer> dpDealIds = dpDealIdsFuture.join();
                    Long dpUserId = dpUserIdFuture.join();
                    Map<Integer, Integer> dp2mtDealIdMap = dp2mtDealIdMapFuture.join();
                    
                    if (CollectionUtils.isEmpty(dpDealIds) || dpUserId <= 0 || MapUtils.isEmpty(dp2mtDealIdMap)) {
                        LOGGER.info("DealUserPurchaseInfoFetcher empty parameters, dpDealIds: {}, dpUserId: {}, dp2mtDealIdMap: {}",
                                dpDealIds, dpUserId, dp2mtDealIdMap);
                        return CompletableFuture.completedFuture(new HashMap<>());
                    }
                    
                    // 查询美团和点评平台的订单
                    CompletableFuture<Map<String, Integer>> mtOrderCountFuture = searchMtOrders(userId, mtShopId, dpDealIds);
                    CompletableFuture<Map<String, Integer>> dpOrderCountFuture = searchDpOrders(dpUserId, dpShopId, dpDealIds);
                    
                    return CompletableFuture.allOf(mtOrderCountFuture, dpOrderCountFuture)
                            .thenApply(v2 -> {
                                Map<String, Integer> mtOrderCount = mtOrderCountFuture.join();
                                Map<String, Integer> dpOrderCount = dpOrderCountFuture.join();
                                
                                // 合并美团和点评的订单统计
                                Map<String, Integer> totalOrderCount = new HashMap<>();
                                if (MapUtils.isNotEmpty(mtOrderCount)) {
                                    totalOrderCount.putAll(mtOrderCount);
                                }
                                
                                if (MapUtils.isNotEmpty(dpOrderCount)) {
                                    // 合并点评订单计数
                                    dpOrderCount.forEach((dealId, count) -> 
                                        totalOrderCount.merge(dealId, count, Integer::sum));
                                }
                                
                                // 构建结果
                                Map<Integer, DealUserPurchaseInfoModel> result = new HashMap<>();
                                for (Map.Entry<Integer, FetchingContext> entry : keyFetchContexts.entrySet()) {
                                    Integer mtDealId = entry.getKey();
                                    // 找到对应的点评团单ID
                                    Integer dpDealId = null;
                                    for (Map.Entry<Integer, Integer> mapEntry : dp2mtDealIdMap.entrySet()) {
                                        if (Objects.equals(mapEntry.getValue(), mtDealId)) {
                                            dpDealId = mapEntry.getKey();
                                            break;
                                        }
                                    }
                                    
                                    if (dpDealId == null) {
                                        continue;
                                    }
                                    
                                    DealUserPurchaseInfoModel model = new DealUserPurchaseInfoModel();
                                    model.setDealId(mtDealId);
                                    model.setDealPurchaseNums(totalOrderCount.getOrDefault(dpDealId.toString(), 0));
                                    result.put(mtDealId, model);
                                }
                                
                                return result;
                            });
                });
    }
    
    /**
     * 获取点评团单ID列表
     */
    private CompletableFuture<List<Integer>> getDpDealIds(Map<Integer, FetchingContext> keyFetchContexts) {
        ExecutionContext executionContext = ContextUtils.getExecutionContext(keyFetchContexts);
        return NodeUtils.getDp2MTDealIdMap(executionContext)
                .thenApply(dp2mtDealIdMap -> {
                    if (MapUtils.isEmpty(dp2mtDealIdMap)) {
                        return new ArrayList<Integer>();
                    }
                    return new ArrayList<>(dp2mtDealIdMap.keySet());
                });
    }
    
    /**
     * 查询美团平台订单
     */
    private CompletableFuture<Map<String, Integer>> searchMtOrders(Long mtUserId, Long mtShopId, List<Integer> dpDealIds) {
        if (mtUserId == null || mtUserId <= 0 || mtShopId == null || mtShopId <= 0 || CollectionUtils.isEmpty(dpDealIds)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        
        FacadeService facadeService = AthenaBeanFactory.getBean(FacadeService.class);
        
        // 构建美团订单查询请求
        OrderSearchRequest request = new OrderSearchRequest();
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("userId", mtUserId.toString());
        reqParams.put("shopId", mtShopId.toString());
        reqParams.put("source", 2); // 美团平台
        reqParams.put("startTime", getStartTime());
        reqParams.put("endTime", new Date());
        reqParams.put("size", MAX_QUERY_SIZE);
        
        for (Map.Entry<String, Object> entry : reqParams.entrySet()) {
            request.addParameter(entry.getKey(), entry.getValue());
        }
        
        SessionContext context = new SessionContext();
        Map<String, String> contextParams = new HashMap<>();
        contextParams.put("clientIp", "127.0.0.1");
        context.setAttributes(contextParams);
        
        return facadeService.searchOrder(request, context)
                .thenApply(this::processOrderResponse)
                .exceptionally(ex -> {
                    LOGGER.error("Failed to search MT orders, mtUserId: {}, mtShopId: {}, error: {}", 
                            mtUserId, mtShopId, ex.getMessage(), ex);
                    return new HashMap<>();
                });
    }
    
    /**
     * 查询点评平台订单
     */
    private CompletableFuture<Map<String, Integer>> searchDpOrders(Long dpUserId, Long dpShopId, List<Integer> dpDealIds) {
        if (dpUserId == null || dpUserId <= 0 || dpShopId == null || dpShopId <= 0 || CollectionUtils.isEmpty(dpDealIds)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        
        FacadeService facadeService = AthenaBeanFactory.getBean(FacadeService.class);
        
        // 构建点评订单查询请求
        OrderSearchRequest request = new OrderSearchRequest();
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("userId", dpUserId.toString());
        reqParams.put("shopId", dpShopId.toString());
        reqParams.put("source", 1); // 点评平台
        reqParams.put("startTime", getStartTime());
        reqParams.put("endTime", new Date());
        reqParams.put("size", MAX_QUERY_SIZE);
        
        for (Map.Entry<String, Object> entry : reqParams.entrySet()) {
            request.addParameter(entry.getKey(), entry.getValue());
        }
        
        SessionContext context = new SessionContext();
        Map<String, String> contextParams = new HashMap<>();
        contextParams.put("clientIp", "127.0.0.1");
        context.setAttributes(contextParams);
        
        return facadeService.searchOrder(request, context)
                .thenApply(this::processOrderResponse)
                .exceptionally(ex -> {
                    LOGGER.error("Failed to search DP orders, dpUserId: {}, dpShopId: {}, error: {}", 
                            dpUserId, dpShopId, ex.getMessage(), ex);
                    return new HashMap<>();
                });
    }
    
    /**
     * 处理订单响应，统计每个商品的购买次数
     */
    private Map<String, Integer> processOrderResponse(OrderSearchResponse response) {
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return new HashMap<>();
        }
        
        // 统计每个spugid的出现次数
        Map<String, Integer> dealIdCountMap = new HashMap<>();
        for (Map<String, String> orderData : response.getData()) {
            String spugid = orderData.get("spugid");
            if (StringUtils.isNotBlank(spugid)) {
                dealIdCountMap.merge(spugid, 1, Integer::sum);
            }
        }
        
        return dealIdCountMap;
    }
    
    /**
     * 获取查询起始时间（180天前）
     */
    private Date getStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -QUERY_TIME_RANGE_DAYS);
        return calendar.getTime();
    }
} 