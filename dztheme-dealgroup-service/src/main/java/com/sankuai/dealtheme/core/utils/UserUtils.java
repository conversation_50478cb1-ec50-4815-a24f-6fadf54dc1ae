package com.sankuai.dealtheme.core.utils;

import com.sankuai.athena.graphql.Execution;
import com.sankuai.dealtheme.themes.config.DealThemeConfig;
import com.sankuai.producttheme.core.utils.ParamAggUtils;
import com.sankuai.producttheme.core.utils.ParamsUtil;

/**
 * 用户工具类
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class UserUtils {

    /**
     * 获取当前用户ID（优先获取美团用户ID）
     * 
     * @param execution 执行上下文
     * @return 用户ID，如果无法获取则返回null
     */
    public static Long getCurrentUserId(Execution execution) {
        if (execution == null) {
            return null;
        }

        // 优先获取美团用户ID
        long mtUserId = ParamAggUtils.getMTUserId(execution);
        if (mtUserId > 0) {
            return mtUserId;
        }

        // 如果美团用户ID不存在，尝试获取点评用户ID
        long dpUserId = ParamAggUtils.getDPUserId(execution);
        if (dpUserId > 0) {
            return dpUserId;
        }

        // 最后尝试获取通用用户ID
        long userId = ParamsUtil.getLongSafely(execution.getParameters(), DealThemeConfig.USER_ID);
        if (userId > 0) {
            return userId;
        }

        return null;
    }

    /**
     * 获取美团用户ID
     * 
     * @param execution 执行上下文
     * @return 美团用户ID，如果无法获取则返回0
     */
    public static long getMTUserId(Execution execution) {
        if (execution == null) {
            return 0L;
        }
        return ParamAggUtils.getMTUserId(execution);
    }

    /**
     * 获取点评用户ID
     * 
     * @param execution 执行上下文
     * @return 点评用户ID，如果无法获取则返回0
     */
    public static long getDPUserId(Execution execution) {
        if (execution == null) {
            return 0L;
        }
        return ParamAggUtils.getDPUserId(execution);
    }
}
