package com.sankuai.dealtheme.core.documents.attr;

import com.sankuai.athena.theme.framework.annotations.Doc;
import com.sankuai.athena.theme.framework.transformer.Record;
import com.sankuai.athena.theme.framework.transformer.document.DocContext;
import com.sankuai.athena.theme.framework.transformer.document.Document;
import com.sankuai.dealtheme.core.documents.utils.DealModelUtil;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.themes.trans.DocumentFields;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;

import java.util.Objects;

/**
 * @description : 用户在一段时间内同门店下购买次数团单次数属性
 * @date : 2025/4/16
 */
@Doc(name = DealUserPurchaseCountAttrDocument.FIELD_NAME,
        description = "用户180天内在门店下的商品购买次数，不区分平台",
        needFields = {"DealUserPurchaseInfoModel"},
        modelField = DocumentFields.ProductFieldNames.attrs
)
public class DealUserPurchaseCountAttrDocument implements Document<DealProductAttrDTO> {
    public static final String FIELD_NAME = "dealUserPurchaseCountAttr";

    @Override
    public DealProductAttrDTO execute(DocContext docContext) {
        Record record = docContext.getSubject();
        DealUserPurchaseInfoModel purchaseModel = getDealUserPurchaseInfoModel(record);
        return buildDealUserOrderCountAttr(purchaseModel);
    }

    private static DealUserPurchaseInfoModel getDealUserPurchaseInfoModel(Record record) {
        if (Objects.isNull(record)) {
            return null;
        }
        DealModel dealModel = DealModelUtil.getDeal(record);
        if (Objects.isNull(dealModel)
                || Objects.isNull(dealModel.getDealUserPurchaseInfoModel())) {
            return null;
        }
        return dealModel.getDealUserPurchaseInfoModel();
    }

    private DealProductAttrDTO buildDealUserOrderCountAttr(DealUserPurchaseInfoModel purchaseModel) {
        DealProductAttrDTO result = new DealProductAttrDTO();
        result.setName(FIELD_NAME);
        result.setValue(Objects.nonNull(purchaseModel) ? String.valueOf(purchaseModel.getDealPurchaseNums()) : "0");
        return result;
    }

}
