package com.sankuai.dealtheme.core.documents;

import com.sankuai.athena.theme.framework.annotations.Document;
import com.sankuai.athena.theme.framework.annotations.Field;
import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.documents.DocumentFields;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.dto.DealProductAttrDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户团单购买次数文案转换器
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Document(name = "dealUserPurchaseCountAttr", target = DocumentFields.ProductFieldNames.attrs)
public class DealUserPurchaseCountDoc {

    /**
     * 将用户购买信息转换为产品属性DTO
     * 
     * @param dealModel 团单模型
     * @return 产品属性DTO
     */
    @Field(name = "dealUserPurchaseCountAttr", description = "用户团单购买次数属性")
    public DealProductAttrDTO getDealUserPurchaseCountAttr(@Param("dealModel") DealModel dealModel) {
        
        if (dealModel == null || dealModel.getDealUserPurchaseInfo() == null) {
            log.debug("DealUserPurchaseCountDoc: dealModel或购买信息为空");
            return null;
        }

        DealUserPurchaseInfoModel purchaseInfo = dealModel.getDealUserPurchaseInfo();
        
        // 构建产品属性DTO
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("dealUserPurchaseCount");
        attrDTO.setValue(String.valueOf(purchaseInfo.getPurchaseCount()));
        
        return attrDTO;
    }

    /**
     * 生成购买次数描述文案
     * 
     * @param purchaseCount 购买次数
     * @return 描述文案
     */
    private String generatePurchaseCountDescription(int purchaseCount) {
        if (purchaseCount == 0) {
            return "您还未购买过此团单";
        } else if (purchaseCount == 1) {
            return "您已购买过1次此团单";
        } else {
            return String.format("您已购买过%d次此团单", purchaseCount);
        }
    }
}
