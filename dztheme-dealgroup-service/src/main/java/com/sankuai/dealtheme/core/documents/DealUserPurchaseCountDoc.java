package com.sankuai.dealtheme.core.documents;

import com.sankuai.athena.theme.framework.annotations.Doc;
import com.sankuai.athena.theme.framework.documents.Document;
import com.sankuai.athena.theme.framework.documents.DocContext;
import com.sankuai.athena.theme.framework.documents.Record;
import com.sankuai.dealtheme.dto.DealProductAttrDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户团单购买次数文案转换器
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Doc(name = "dealUserPurchaseCountAttr",
     needFields = {"dealUserPurchaseInfo"},
     modelField = "attrs")
public class DealUserPurchaseCountDoc implements Document<DealProductAttrDTO> {

    @Override
    public DealProductAttrDTO execute(DocContext docContext) {
        Record record = docContext.getSubject();

        // 从record中获取用户购买信息
        Object purchaseInfoObj = record.getField("dealUserPurchaseInfo");
        if (purchaseInfoObj == null) {
            log.debug("DealUserPurchaseCountDoc: 用户购买信息为空");
            return null;
        }

        try {
            // 假设purchaseInfoObj是一个包含purchaseCount字段的对象
            Integer purchaseCount = 0;
            if (purchaseInfoObj instanceof Record) {
                Record purchaseInfoRecord = (Record) purchaseInfoObj;
                Object countObj = purchaseInfoRecord.getField("purchaseCount");
                if (countObj instanceof Integer) {
                    purchaseCount = (Integer) countObj;
                }
            }

            // 构建产品属性DTO
            DealProductAttrDTO attrDTO = new DealProductAttrDTO();
            attrDTO.setName("dealUserPurchaseCount");
            attrDTO.setValue(String.valueOf(purchaseCount));

            return attrDTO;
        } catch (Exception ex) {
            log.error("DealUserPurchaseCountDoc: 处理用户购买信息失败", ex);
            return null;
        }
    }
}
