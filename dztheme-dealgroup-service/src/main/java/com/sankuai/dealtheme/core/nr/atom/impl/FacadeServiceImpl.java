package com.sankuai.dealtheme.core.nr.atom.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.aqc.license.api.ShopLicenseService;
import com.dianping.aqc.license.dto.ShopLicenseNewDTO;
import com.dianping.aqc.license.enums.AuthStatusEnum;
import com.dianping.aqc.license.enums.LicenseCategoryEnum;
import com.dianping.baby.timeLimitTg.api.BabyHotSaleTgService;
import com.dianping.baby.timeLimitTg.dto.BabyHotSaleTgDTO;
import com.dianping.beauty.deal.biz.dto.BeautyTagMap;
import com.dianping.beauty.deal.biz.service.BeautyTagMapService;
import com.dianping.beauty.tag.bean.BeautyTagItem;
import com.dianping.beauty.tag.service.BeautySearchService;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.attribute.service.DealGroupAttributeTextService;
import com.dianping.deal.detail.DealGroupDetailService;
import com.dianping.deal.detail.dto.*;
import com.dianping.deal.idmapper.api.DealGroupContentRelationQueryService;
import com.dianping.deal.sale.api.dto.ProductSaleDto;
import com.dianping.deal.sale.api.dto.QueryProductSaleRequest;
import com.dianping.deal.sale.api.service.ProductSaleQueryService;
import com.dianping.deal.sales.common.datatype.*;
import com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService;
import com.dianping.deal.sales.display.api.service.SalesDisplayQueryService;
import com.dianping.deal.sales.display.api.service.ShopSceneSalesDisplayService;
import com.dianping.deal.sales.display.api.service.SubjectSalesDisplayService;
import com.dianping.deal.shop.DealGroupBestShopQueryService;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.deal.tag.DealMetaTagQueryService;
import com.dianping.deal.tag.SubjectTagJudgeService;
import com.dianping.deal.tag.TagQueryService;
import com.dianping.deal.tag.dto.*;
import com.dianping.deal.volume.query.api.AccumSaleQueryService;
import com.dianping.deal.volume.query.api.CycleSaleQueryService;
import com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService;
import com.dianping.deal.voucher.query.api.dto.VoucherDTO;
import com.dianping.general.data.rec.api.exception.ParamException;
import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.gm.bonus.exposure.api.dto.ConsumeBonusSimpleListDTO;
import com.dianping.gm.bonus.exposure.api.service.BonusConsumeSearchService;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityResponse;
import com.dianping.gmkt.activity.api.request.CheckDealActivityRelationRequest;
import com.dianping.gmkt.activity.api.request.CheckDealItemRelationRequest;
import com.dianping.gmkt.activity.api.request.QueryActivityRequest;
import com.dianping.gmkt.activity.api.response.CheckDealActivityRelationResponse;
import com.dianping.gmkt.activity.api.response.CheckDealItemRelationResponse;
import com.dianping.gmkt.activity.api.response.QueryActivityResponse;
import com.dianping.gmkt.activity.api.service.DealActivityQueryService;
import com.dianping.gmkt.activity.api.service.DealListService;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.joy.category.process.api.bar.BarProductManagerService;
import com.dianping.joy.category.process.api.dto.bar.barProductManage.GetUrlReqDTO;
import com.dianping.joy.category.process.api.dto.bar.barProductManage.GetUrlRespDTO;
import com.dianping.joygeneral.api.insurance.UniversalInsuranceService;
import com.dianping.joygeneral.api.insurance.dto.ReadInsuranceReqDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsQueryDTO;
import com.dianping.ktv.gather.api.service.SaleStatisticsService;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.pigeon.remoting.common.codec.SerializerType;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.poi.areacommon.AreaCommonService;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.dto.activity.ActivityShelfToCDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTOList;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.common.request.activities.ProductQueryByShopIdToCRequest;
import com.dianping.product.shelf.common.request.subjectManage.SubjectConfigQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.dianping.product.shelf.query.api.SubjectConfigManageService;
import com.dianping.reviewremote.remote.ReviewDealGroupServiceV2;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.technician.biz.dto.TechnicianDetailDTO;
import com.dianping.technician.service.TechnicianGrouponService;
import com.dianping.tpfun.product.api.category.ProductFrontCategoryService;
import com.dianping.tpfun.product.api.category.model.ProductCategory;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.aggregate.ProductDetailService;
import com.dianping.tpfun.product.api.sku.aggregate.dto.ProductUrlDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductUrlRequest;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.platform.resource.ResourceQueryService;
import com.dianping.tpfun.product.api.sku.platform.resource.dto.ResourceInfoDTO;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.tpfun.product.api.sku.request.GetProductItemRequest;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.dianping.ugc.review.remote.enums.ReviewPlatFormEnum;
import com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.userremote.base.service.UserService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.dianping.vc.sdk.data.Converters;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.mobile.sinai.base.common.PoiFields;
import com.meituan.service.mobile.sinai.client.PoiClientL;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.beautycontent.security.displaycontrol.api.DisplayControlService;
import com.sankuai.beautycontent.security.displaycontrol.request.DisplayControlBatchDealRequest;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlBatchDealResponse;
import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.clr.content.process.thrift.api.ContentProcessService;
import com.sankuai.clr.content.process.thrift.api.LeadsQueryService;
import com.sankuai.clr.content.process.thrift.api.ResvQueryService;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryLatestStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryPlanListReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryResvBookingTagReqDTO;
import com.sankuai.clr.content.process.thrift.dto.req.QueryStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryLatestStockRespDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryPlanListRespDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryResvBookingTagRespDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.QueryStockRespDTO;
import com.sankuai.dealtheme.core.documents.utils.ExUtils;
import com.sankuai.dealtheme.core.fetchers.model.DealCustomStructModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.dealtheme.core.utils.LiveUtils;
import com.sankuai.dealtheme.themes.facade.LogControl;
import com.sankuai.dealtheme.themes.monitor.DealProductReqMonitor;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.TradePriceService;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.fbi.faas.wed.api.SmartPictureQueryService;
import com.sankuai.fbi.faas.wed.dto.PictureBatchQueryReqDTO;
import com.sankuai.fbi.faas.wed.dto.PictureRespDTO;
import com.sankuai.fbi.faas.wed.dto.ServiceResponse;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.WashingWhiteCustomerListRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.api.MiniprogramLinkService;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.ApiResponse;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.BatchMiniprogramJumpLinkRequest;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.MiniprogramJumpLinkDTO;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.service.QueryDataSyncService;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.insurance.adapter.dc.base.SignInfo;
import com.sankuai.insurance.adapter.dc.service.search.ylx.YlxPackageSearchRequest;
import com.sankuai.insurance.adapter.dc.service.search.ylx.YlxPackageSearchResp;
import com.sankuai.insurance.adapter.dc.service.search.ylx.YlxPackageSearchService;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.count.thrift.dto.req.QueryLeadsSalesReqDTO;
import com.sankuai.leads.count.thrift.dto.resp.QueryLeadsSalesRespDTO;
import com.sankuai.lifeevent.communitylife.dealgroup.channel.api.LifeFulfillmentInfoQueryService;
import com.sankuai.lifeevent.communitylife.dealgroup.channel.api.dto.reserve.DealGroupEarliestReserveTimeReq;
import com.sankuai.lifeevent.communitylife.dealgroup.channel.api.dto.responses.DealGroupEarliestReserveTimeResp;
import com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSimpleDTO;
import com.sankuai.merchantcard.timescard.exposure.req.QueryCardSimpInfoRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadContentByExternalBizIdDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import com.sankuai.mpmctcontent.query.thrift.api.ContentAggregate2CService;
import com.sankuai.mpmctcontent.query.thrift.api.digest.DigestQueryService;
import com.sankuai.mpmctcontent.query.thrift.api.meta.MetaInfoService;
import com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService;
import com.sankuai.mpmctcontent.query.thrift.dto.TagValueDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.QueryContentDetailReqDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.QueryContentDetailRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.TagValueRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.MultiParam;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchRequestDTOBuilder;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedExhibitsReq;
import com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedExhibitsResp;
import com.sankuai.mpmctexhibit.process.request.GetRelateExhibitInfoListReq;
import com.sankuai.mpmctexhibit.process.response.GetRelateExhibitInfoListResp;
import com.sankuai.mpmctexhibit.process.service.ExhibitCustomerService;
import com.sankuai.mpmctexhibit.process.service.baby.BabyExhibitService;
import com.sankuai.mpmctexhibit.process.service.dealgroup.DealGroupExhibitService;
import com.sankuai.mpmctmember.process.thrift.nib.user.api.NibMemberProductQryThriftService;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.BatchGetGrouponMemberDiscountInfoReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.BatchVerifyProductMemberExclusiveReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.BatchGetGrouponMemberDiscountInfoRespDTO;
import com.sankuai.mpmctmvacommon.resource.response.constant.CommonRespCodeEnum;
import com.sankuai.mpmctpoiext.info.query.thrift.biz.car.api.RentalCarToCRemoteService;
import com.sankuai.mpmctpoiext.info.query.thrift.biz.car.dto.DepositRuleQueryReqDTO;
import com.sankuai.mpmctpoiext.info.query.thrift.biz.car.dto.RentalCarDepositRuleDTO;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionProxyService;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionRequest;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.swan.udqs.api.*;
import com.sankuai.swan.udqs.api.integration.IntegrationParams;
import com.sankuai.technician.trade.api.product.dto.TechProductUrlMultiDTO;
import com.sankuai.technician.trade.api.product.request.TechProductUrlBatchQuery;
import com.sankuai.technician.trade.api.product.service.TechProductQueryService;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserBatchRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.zdc.produce.api.PoiQueryService;
import com.sankuai.zdc.produce.dto.ShopServiceRegionDTO;
import graphql.execution.Async;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dealtheme.core.fetchers.utils.DealGroupQueryCenterUtils.QUERY_CENTER;
import static com.sankuai.dealtheme.core.utils.LiveConstants.LIVE_RPC;

/**
 * Created by zhaizhui on 2020/8/17
 */
@Service
public class FacadeServiceImpl implements FacadeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FacadeServiceImpl.class);

    private ExecutorService bpDealGroupFetchPool = ExecutorServices.forThreadPoolExecutor("bpDealGroupFetchPool");

    private ExecutorService bpDealGroupFetchPool2 = ExecutorServices.forThreadPoolExecutor("bpDealGroupFetchPool2");

    private ExecutorService bpDealGroupFetchPool3 = ExecutorServices.forThreadPoolExecutor("bpDealGroupFetchPool3");

    //团购货架线程池
    private ExecutorService dealCommonPool = ExecutorServices.forThreadPoolExecutor("dealCommonPool");

    public static final ExecutorService liveRpcPool = ExecutorServices.forThreadPoolExecutor("liveRpcPool");

    public static final List<String> POI_FIELDS = Lists.newArrayList(
            PoiFields.poiid, PoiFields.addr
    );

    @RpcClient(url = "http://service.dianping.com/tpfunService/productdetailservice_1.0.0")
    private ProductDetailService productDetailService;

    @RpcClient(url = "http://service.dianping.com/dealAttributeService/dealGroupAttributeTextService_1.0.0")
    private DealGroupAttributeTextService dealGroupAttributeTextServiceFuture;

    @RpcClient(url = "http://service.dianping.com/bonusExposureService/BonusConsumeSearchService_1.0.0")
    private BonusConsumeSearchService bonusConsumeSearchService;

    @RpcClient(url = "com.dianping.deal.volume.query.api.AccumSaleQueryService")
    private AccumSaleQueryService accumSaleQueryFutureService;

    @RpcClient(url = "com.dianping.deal.volume.query.api.CycleSaleQueryService")
    private CycleSaleQueryService cycleSaleQueryService;

    @RpcClient(url = "com.dianping.poi.relation.service.api.PoiRelationService")
    private PoiRelationService poiRelationService;

    @RpcClient(url = "beauty.tag.search.beautySearchService")
    private BeautySearchService beautySearchService;

    @RpcClient(url = "com.dianping.deal.sales.display.api.service.ShopSceneSalesDisplayService")
    private ShopSceneSalesDisplayService shopSceneSalesDisplayService;

    @RpcClient(url = "com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService")
    private ProductSceneSalesDisplayService productSceneSalesDisplayService;

    @RpcClient(url = "com.dianping.deal.sales.display.api.service.SubjectSalesDisplayService")
    private SubjectSalesDisplayService subjectSalesDisplayService;

    @RpcClient(url = "com.dianping.deal.sales.display.api.service.SalesDisplayQueryService")
    private SalesDisplayQueryService salesDisplayQueryService;

    @Resource
    private ProductSceneSalesDisplayService productSceneSalesDisplayServiceLive;

    @RpcClient(url = "com.dianping.deal.sale.api.service.ProductSaleQueryService")
    private ProductSaleQueryService productSaleQueryService;

    @RpcClient(url = "http://service.dianping.com/payPromoDisplayService/PromoDisplayService_1.0.0")
    private PromoDisplayService promoDisplayServiceInSH;

    @RpcClient(url = "http://service.dianping.com/gmkt_activity_service/DealActivityQueryService_0.0.1")
    private DealActivityQueryService dealActivityQueryService;

    @RpcClient(url = "http://service.dianping.com/gmkt_activity_service/DealListService_0.0.1", timeout = 200)
    private DealListService dealListService;

    @RpcClient(url = "beauty.deal.biz.service.BeautyTagMapService", timeout = 500)
    private BeautyTagMapService beautyTagMapService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/productFrontCategoryService_1.0.0")
    private ProductFrontCategoryService productFrontCategoryService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctexhibit.process")
    private ExhibitCustomerService exhibitCustomerService;

    @RpcClient(url = "http://service.dianping.com/ZDCProduceService/poiQueryService_1.0.0")
    private PoiQueryService poiQueryService;

    @RpcClient(url = "http://service.dianping.com/martgeneral/recommend_1.0.0")
    private RecommendService recommendService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.leads.content.process",
            timeout = 1000
    )
    private LeadsQueryService leadsQueryService;

    @Resource
    private HaimaClient haimaClient;

    @RpcClient(url = "com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService")
    private DealGroupVoucherQueryService dealGroupVoucherQueryService;

    @RpcClient(url = "http://service.dianping.com/userAccountService/userAccountService_2.0.0")
    private UserAccountService userAccountService;

    @RpcClient(remoteAppkey = "com.sankuai.wpt.user.retrieve")
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmktproxy.query")
    private PromotionProxyService.Iface promotionProxyService;

    @Resource
    private YlxPackageSearchService ylxPackageSearchService;

    @RpcClient(url = "http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0")
    private AreaCommonService areaCommonService;

    @RpcClient(url = "com.dianping.deal.tag.DealMetaTagQueryService")
    private DealMetaTagQueryService dealMetaTagQueryService;

    @RpcClient(url = "http://service.dianping.com/TimesCardNavigationService/TimesCardNavigationService_1.0.0")
    private TimesCardNavigationService timesCardNavigationService;

    @RpcClient(url = "http://service.dianping.com/tuangou/dealShopService/dealGroupBestShopQueryService_1.0.0")
    private DealGroupBestShopQueryService dealGroupBestShopQueryService;

    @RpcClient(url = "http://service.dianping.com/gisService/cityInfoService_1.0.0")
    private CityInfoService cityInfoService;

    @RpcClient(url = "com.sankuai.dztheme.shop.service.DzThemeShopService")
    private DzThemeShopService dzThemeShopService;

    @Resource
    private DzThemeShopService dzThemeShopServiceLive;

    @RpcClient(url = "http://service.dianping.com/baby/hotsale/babyHotSaleTgService_1.0.0")
    private BabyHotSaleTgService babyHotSaleTgService;

    @Resource
    private PoiClientL poiClientL;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctexhibit.process")
    private DealGroupExhibitService dealGroupExhibitService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctexhibit.query", timeout = 500)
    private com.sankuai.mpmctexhibit.query.service.DealGroupExhibitService fastDealGroupExhibitService;

    @RpcClient(url = "ReviewService.ReviewDealGroupService")
    private ReviewDealGroupServiceV2 reviewDealGroupServiceV2;

    @RpcClient(url = "UGCReviewService.MTReviewQueryServiceV2")
    private MTReviewQueryServiceV2 mtReviewQueryServiceV2;

    @RpcClient(url = "com.sankuai.beautycontent.security.displaycontrol.api.DisplayControlService")
    private DisplayControlService displayControlService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctpoiext.info.query")
    private RentalCarToCRemoteService rentalCarToCRemoteService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.lifeevent.faas.wed", timeout = 1000)
    private MiniprogramLinkService miniprogramLinkService;

    @RpcClient(url = "http://service.dianping.com/DealGroupContentRelationQueryService/DealGroupContentRelationQueryService_1.0.0")
    private DealGroupContentRelationQueryService dealGroupContentRelationQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctcontent.query")
    private ContentAggregate2CService contentAggregate2CService;

    @Resource
    @Qualifier("contentSearchService")
    private ContentSearchService contentSearchService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mpmctcontent.query", timeout = 1000)
    private DigestQueryService digestQueryService;

    @MdpPigeonClient(remoteAppKey = "com.sankuai.lifeevent.faas.life", serialize = SerializerType.THRIFT, callType = CallMethod.SYNC, timeout = 1000)
    private ReserveConfigQueryService reserveConfigQueryService;

    @Resource
    @Qualifier("metaInfoService")
    private MetaInfoService metaInfoService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctexhibit.process")
    private BabyExhibitService babyExhibitService;

    @RpcClient(url = "com.dianping.deal.tag.TagQueryService")
    private TagQueryService tagQueryService;

    @RpcClient(url = "http://service.dianping.com/sku/platform/resource/resourceQueryService_1.0.0")
    private ResourceQueryService resourceQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.mtusercenter.info.batchretrieve")
    private RpcUserBatchRetrieveService.Iface rpcUserBatchRetrieveService;

    @RpcClient(url = "http://service.dianping.com/userBaseService/userService_2.0.0")
    private UserService userService;

    @RpcClient(url = "http://service.dianping.com/tuangou/dztgUsercenterService/createOrderPageUrlService_1.0.0")
    private CreateOrderPageUrlService createOrderPageUrlService;

    @Resource
    private CreateOrderPageUrlService createOrderPageUrlServiceLive;

    @MdpThriftClient(timeout = 1000, testTimeout = 5000, remoteAppKey = "com.sankuai.productuser.query.center")
    private DealGroupQueryService bpDealGroupQueryService;

    @Autowired
    private DealGroupQueryService dealGroupQueryService2;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctmember.process", timeout = 1000)
    private NibMemberProductQryThriftService nibMemberProductQryThriftService;

    @RpcClient(remoteAppkey = "com.sankuai.sinai.data.query", timeout = 1000)
    private DpPoiService dpPoiService;

    @RpcClient(url = "com.sankuai.swan.udqs.api.SwanQueryService")
    private SwanQueryService swanQueryService;

    @RpcClient(url = "com.dianping.technician.service.TechnicianGrouponService")
    private TechnicianGrouponService technicianGrouponService;

    @RpcClient(remoteAppkey = "com.sankuai.lifeevent.faas.wed", timeout = 5000)
    private SmartPictureQueryService smartPictureQueryService;

    @RpcClient(url = "com.sankuai.dztheme.deal.DealSkuService")
    private DealSkuService dealSkuService;

    @Resource
    private ResvQueryService resvQueryService;

    @RpcClient(url = "com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery", timeout = 1000)
    private RankLabelQuery rankLabelQuery;

    @RpcClient(url = "http://service.dianping.com/joy-category-process-service/BarProductManagerService_1.0.0", timeout = 1000)
    private BarProductManagerService barProductManagerService;

    @RpcClient(remoteAppkey = "com.sankuai.priceoperation.service")
    private GuaranteeQueryService guaranteeQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.mtusercenter.merge.query")
    private UserMergeQueryService.Iface userMergeQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.lifeevent.faas.life")
    private LifeFulfillmentInfoQueryService lifeFulfillmentInfoQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.feitianplus.data.onedata", timeout = 8000)
    private QueryDataSyncService.Iface queryDataSyncService;

    @Resource
    private GuaranteeQueryService guaranteeQueryServiceLive;

    @RpcClient(url = "com.sankuai.joygeneral.insurance.UniversalInsuranceService")
    private UniversalInsuranceService universalInsuranceService;

    private static final SwanUniteQueryService swanUniteQueryService = new SwanUniteQueryService();

    @Resource
    @Qualifier(value = "sinaiDpPoiServiceLive")
    private DpPoiService sinaiDpPoiServiceLive;


    @RpcClient(url = "http://service.dianping.com/aqc-license-service/shopLicenseService_1.0.0")
    private ShopLicenseService shopLicenseService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.leads.count",
            remoteServerPort = 9001,
            timeout = 1000
    )
    private NewLeadsCountService newLeadsCountService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.mpmctcontent.application",
            timeout = 1000
    )
    private DealDetailPageGWService dealDetailPageGWService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.leads.content.process",
            timeout = 1000
    )
    private ContentProcessService contentProcessService;

    @Resource
    private PlayCenterService.Iface playCenterService;

    @RpcClient(url = "com.dianping.product.shelf.query.api.ActivityShelfQueryService")
    private ActivityShelfQueryService activityShelfQueryService;

    @Resource
    private SubjectConfigManageService subjectConfigManageService;

    @RpcClient(url = "com.dianping.deal.tag.SubjectTagJudgeService")
    private SubjectTagJudgeService subjectTagJudgeService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/skuProductService_1.0.0")
    private ProductService productService;

    @RpcClient(url = "http://service.dianping.com/ktvGatherService/saleStatisticsRemoteService_1.0.0")
    private SaleStatisticsService saleStatisticsService;

    @RpcClient(url = "com.sankuai.technician.trade.api.product.service.TechProductQueryService")
    private TechProductQueryService techProductQueryService;

    @Override
    public CompletableFuture<List<DpPoiDTO>> multiShopUuid(List<Long> dpShopIds, Execution execution) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.multiShopUuid(java.util.List,com.sankuai.athena.graphql.Execution)");
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        if (LiveUtils.liveReqIndependentClient(execution)) {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Cat.logEvent(LIVE_RPC, "batchLoadByShopId");
                    return batchQueryShopUUidForLive(dpShopIds);
                } catch (Exception ex) {
                    Cat.logEvent(LIVE_RPC, "batchLoadByShopId-error");
                    LOGGER.error("multiGetSales-error,req:{}", JsonCodec.encode(dpShopIds), ex);
                    return Lists.newArrayList();
                }
            }, liveRpcPool);
        } else {
            return batchQueryShopUUid(dpShopIds);
        }
    }

    @Override
    public CompletableFuture<List<DpPoiDTO>> batchQueryShopUUid(List<Long> dpShopIds) {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        try {
            DpPoiRequest dpPoiRequest = new DpPoiRequest();
            dpPoiRequest.setShopIds(dpShopIds);
            dpPoiRequest.setFields(Lists.newArrayList("shopId", "uuid"));
            return AthenaInf.getRpcCompletableFuture(dpPoiService.findShopsByShopIds(dpPoiRequest))
                    .thenApply(dpPoiDTOS -> {
                        if (CollectionUtils.isEmpty(dpPoiDTOS)) {
                            return Lists.newArrayList();
                        }
                        return dpPoiDTOS;
                    });
        } catch (Exception e) {
            LOGGER.error("[FacadeServiceImpl] batchQueryShopUUid err={}, dpShopIds={}", e, dpShopIds);
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
    }

    private List<DpPoiDTO> batchQueryShopUUidForLive(List<Long> dpShopIds) throws TException {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return Lists.newArrayList();
        }
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpShopIds);
        dpPoiRequest.setFields(Lists.newArrayList("shopId", "uuid"));
        return sinaiDpPoiServiceLive.findShopsByShopIds(dpPoiRequest);
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> batchQueryRecommendInfo(int bizTypeId, String queryKey, IntegrationParams params) {
        return CompletableFuture.supplyAsync(() -> {
            Result<QueryData> queryDataResult = swanUniteQueryService.swanQueryWithoutFormat(bizTypeId, queryKey, params);
            if (!queryDataResult.isIfSuccess()
                    || queryDataResult.getData() == null
                    || CollectionUtils.isEmpty(queryDataResult.getData().getResultSet())) {
                return Lists.newArrayList();
            }
            return queryDataResult.getData().getResultSet();
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> batchQuerySwanProduct(int bizTypeId, String queryKey, SwanParam params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Result<QueryData> queryDataResult = swanUniteQueryService.swanQuery(bizTypeId, queryKey, params);
                if (!queryDataResult.isIfSuccess()
                        || queryDataResult.getData() == null
                        || CollectionUtils.isEmpty(queryDataResult.getData().getResultSet())) {
                    return Lists.newArrayList();
                }
                return queryDataResult.getData().getResultSet();
            } catch (Exception ex) {
                Map param = Maps.newHashMap();
                param.put("bizTypeId", bizTypeId);
                param.put("queryKey", queryKey);
                param.put("swanParam", params);
                commonExceptionHandler("swanQuery", JsonCodec.encode(param), ex);
                return Lists.newArrayList();
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<Map<Integer, DealGroupAvailableDateDTO>> batchGetProductAvailableDates(List<Integer> dpDealGroupIds) {
        return AthenaInf.getRpcCompletableFuture(dealGroupDetailService.multiGetDealGroupAvailableDates(dpDealGroupIds, true))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchGetProductAvailableDates", JsonCodec.encode(dpDealGroupIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<Map<Long, UserDTO>> batchGetUserMap(List<Long> dpUserIds) {
        return AthenaInf.getRpcCompletableFuture(userService.getUserMap(dpUserIds))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchGetUserMap", JsonCodec.encode(dpUserIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<List<UserModel>> batchQueryUserInfoByMtUserIds(List<Long> mtUserIds, UserFields fields) {
        try {
            return AthenaInf.getRpcCompletableFuture(rpcUserBatchRetrieveService.batchGetUserByIds(mtUserIds, fields))
                    .exceptionally(ex -> {
                        commonExceptionHandler("batchQueryUserInfoByMtUserIds", String.format("mtUserIds:%s,fields:%s", JsonCodec.encode(mtUserIds), JsonCodec.encode(fields)), ex);
                        return new ArrayList<>();
                    });
        } catch (Exception e) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
    }

    @Override
    public CompletableFuture<List<ProductSaleDto>> batchQueryCompleteProductSale(QueryProductSaleRequest request) {
        return AthenaInf.getRpcCompletableFuture(productSaleQueryService.multiQueryCompleteProductSale(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQueryCompleteProductSale", JsonCodec.encode(request), ex);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || response.getResult() == null) {
                        return new ArrayList<>();
                    }
                    return response.getResult().getProductSaleDtoList();
                });
    }

    @Override
    public CompletableFuture<DisplayControlBatchDealResponse> batchGetDisplayControlStatus(DisplayControlBatchDealRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getDealGroupIds())) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(displayControlService.batchDisplayCheck(request)).thenApply(response -> {
            if (response == null) {
                return null;
            }
            return response.getData();
        });
    }

    public CompletableFuture<Map<Long, List<Long>>> bathGetDpShopIdsByMtIdsL(List<Long> mtShopIds) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return CompletableFuture.completedFuture(Collections.EMPTY_MAP);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryDpByMtIdsL(mtShopIds));
        } catch (Exception e) {
            return CompletableFuture.completedFuture(Collections.EMPTY_MAP);
        }
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> queryMtShopIdByDpIdsLong(List<Long> dpShopIds) {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryMtByDpIdsL(dpShopIds));
        } catch (Exception e) {
            LOGGER.error("点评shopId转换美团错误", e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @RpcClient(url = "com.sankuai.dealuser.price.display.api.PriceDisplayService")
    private PriceDisplayService priceDisplayService;

    @RpcClient(url = "com.sankuai.dealuser.price.display.api.TradePriceService")
    private TradePriceService tradePriceService;

    @RpcClient(url = "com.sankuai.dztheme.deal.dealProductBizQueryService")
    private DealProductBizQueryService dealProductBizQueryService;

    @RpcClient(url = "http://service.dianping.com/tuangou/dealService/dealGroupDetailService_1.0.0")
    private DealGroupDetailService dealGroupDetailService;

    @Override
    public CompletableFuture<Map<Integer, ConsumeBonusSimpleListDTO>> batchGetConsumeBonusInfoByDealGroupIds(List<Integer> dpDealGroupIds) {
        return AthenaInf.getRpcCompletableFuture(bonusConsumeSearchService.queryConsumeBonusInfoByDealGroupIds(dpDealGroupIds))
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || MapUtils.isEmpty(response.getData())) {
                        return Maps.newHashMap();
                    }
                    return response.getData();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, DealCustomStructModel>> queryStructData(List<Integer> dealGroupIds, int configId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.queryStructData(java.util.List,int)");
        return CompletableFuture.completedFuture(Maps.newHashMap());
    }

    @Override
    public CompletableFuture<HaimaResponse> getHaimaResponse(HaimaRequest haimaRequest) {
        return CompletableFuture.supplyAsync(() -> haimaClient.query(haimaRequest), dealCommonPool);
    }

    @Override
    public CompletableFuture<ShopServiceRegionDTO> queryShopRegion(long shopId) {
        return AthenaInf.getRpcCompletableFuture(poiQueryService.getServiceRegion(shopId))
                .exceptionally(e -> {
                    commonExceptionHandler("queryShopRegion", String.valueOf(shopId), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isSuccess()) {
                        return null;
                    }
                    return response.getData();
                });
    }

    public CompletableFuture<Map<KeyParam, SpuSale>> multiGetBySpuProduct(List<KeyParam> keyParams, int productType, int saleType, int dimension) {
        return AthenaInf.getRpcCompletableFuture(accumSaleQueryFutureService.multiGetBySpuProduct(keyParams, productType, saleType, dimension));
    }

    @Override
    public CompletableFuture<Map<KeyParam, SpuSale>> multiGetBySpuProductCycle(List<KeyParam> keyParams, int productType, int saleType, int dimension) {
        return AthenaInf.getRpcCompletableFuture(cycleSaleQueryService.multiGetBySpuProduct(keyParams, productType, saleType, dimension));
    }

    @Override
    public CompletableFuture<Map<Long, PoiModelL>> listPoisL(List<Long> mtShopIds, List<String> fields) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<PoiModelL> poiModelLs = poiClientL.listPoisL(mtShopIds, fields);
                return poiModelLs.stream().collect(Collectors.toMap(PoiModelL::getId, Function.identity()));

            } catch (Exception e) {
                LOGGER.error("查询mt商户信息错误，mtShopIds={}", JsonCodec.encode(mtShopIds), e);
                return Maps.newHashMap();
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<List<ReviewStarDistributionDTO>> getDpReviewStar(List<Integer> dpDealIds) {
        return AthenaInf.getRpcCompletableFuture(reviewDealGroupServiceV2.getReviewStarByReferIds(dpDealIds));
    }

    @Override
    public CompletableFuture<Map<Integer, ReviewCount>> getMtReviewStar(List<Integer> mtDealIds) {
        return AthenaInf.getRpcCompletableFuture(mtReviewQueryServiceV2.getReviewCountByDealIds(mtDealIds, ReviewPlatFormEnum.MT.value));
    }

    @Override
    public CompletableFuture<Map<Long, DpPoiDTO>> batchGetShopInfoByShopIds(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(shopIds);
        dpPoiRequest.setFields(Lists.newArrayList("shopId", "useType"));
        try {
            return AthenaInf.getRpcCompletableFuture(dpPoiService.findShopsByShopIds(dpPoiRequest)).thenApply(dpPoiDTOS -> {
                return dpPoiDTOS.stream().collect(Collectors.toMap(DpPoiDTO::getShopId, dpPoiDTO -> dpPoiDTO));
            });
        } catch (Exception e) {
            LOGGER.error("查询商户信息错误，shopIds={}", JsonCodec.encode(shopIds), e);
            return CompletableFuture.completedFuture(new HashMap<>());
        }
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> querySwanDataResult(Integer bizTypeId, String queryKey, SwanParam swanParam) {
        return AthenaInf.getRpcCompletableFuture(swanQueryService.queryByKey(bizTypeId, queryKey, swanParam))
                .exceptionally(e -> {
                    String message = String.format("查询推荐swan信息，bizTypeId：%d，queryKey：%d，swanParam：%d", bizTypeId, queryKey, swanParam);
                    commonExceptionHandler("querySwanDataResult", message, e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isIfSuccess() || response.getData() == null || CollectionUtils.isEmpty(response.getData().getResultSet())) {
                        return Lists.newArrayList(new HashMap<>());
                    }
                    return response.getData().getResultSet();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, Boolean>> getBabyDealHasOutdoorTheme(List<Integer> dpDealIds) {
        if (CollectionUtils.isEmpty(dpDealIds)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        GetBindedExhibitsReq req = new GetBindedExhibitsReq();
        req.setDealGroupIds(dpDealIds);
        return AthenaInf.getRpcCompletableFuture(babyExhibitService.checkDealHasOutdoorTheme(req))
                .exceptionally(ex -> {
                    commonExceptionHandler("getBabyDealHasOutdoorTheme", JsonCodec.encode(req), ex);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || MapUtils.isEmpty(response.getDealHasOutdoorSceneMap())) {
                        return Maps.newHashMap();
                    }
                    return response.getDealHasOutdoorSceneMap();
                });

    }

    @Override
    public CompletableFuture<Map<Long, List<MetaTagTreeByProductDTO>>> batchQueryProductTreeTag(List<Long> dpDealIds) {
        if (CollectionUtils.isEmpty(dpDealIds)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        return AthenaInf.getRpcCompletableFuture(tagQueryService.batchQueryProductTreeTagFromCache(dpDealIds, 1))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQueryProductTreeTag", JsonCodec.encode(dpDealIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<List<ResourceInfoDTO>> batchQueryResourceInfo(BatchQueryResourceInfoRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getResourceIds())) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(resourceQueryService.batchQueryResourceInfo(request))
                .exceptionally(
                        ex -> {
                            commonExceptionHandler("batchQueryResourceInfo", JsonCodec.encode(request), ex);
                            return null;
                        }
                )
                .thenApply(response -> {
                    if (response == null || !response.isSuccess()) {
                        return Lists.newArrayList();
                    }
                    return response.getResult();
                });
    }

    @Override
    public CompletableFuture<Map<String, String>> batchGetCreateOrderPageUrl(BatchGetCreateOrderPageUrlReq request, Execution execution) {
        if (request == null) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        if (LiveUtils.liveReqIndependentClient(execution)) {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Cat.logEvent(LIVE_RPC, "batchGetCreateOrderPageUrl");
                    com.dianping.tuangu.dztg.usercenter.api.dto.Response<Map<String, String>> response =
                            createOrderPageUrlServiceLive.batchGetCreateOrderPageUrl(request);
                    LogControl.logFucByUser(execution, () -> LOGGER.info("batchGetCreateOrderPageUrl,request:{},response:{}",
                            SafeJacksonUtils.serialize(request), JsonCodec.encode(response)));
                    if (response == null || !response.isSuccess()) {
                        return new HashMap<>();
                    }
                    return response.getContent();
                } catch (Exception ex) {
                    Cat.logEvent(LIVE_RPC, "batchGetCreateOrderPageUrl-error");
                    LOGGER.error("batchGetCreateOrderPageUrl-error,req:{}", JsonCodec.encode(request), ex);
                    return Maps.newHashMap();
                }
            }, liveRpcPool);
        }
        return AthenaInf.getRpcCompletableFuture(createOrderPageUrlService.batchGetCreateOrderPageUrl(request))
                .exceptionally(
                        ex -> {
                            commonExceptionHandler("batchGetCreateOrderPageUrl", JsonCodec.encode(request), ex);
                            return new com.dianping.tuangu.dztg.usercenter.api.dto.Response<>();
                        }
                )
                .thenApply(response -> {
                    LogControl.logFucByUser(execution, () -> LOGGER.info("batchGetCreateOrderPageUrl,request:{},response:{}",
                            SafeJacksonUtils.serialize(request), JsonCodec.encode(response)));
                    if (response == null || !response.isSuccess()) {
                        return new HashMap<>();
                    }
                    return response.getContent();
                });
    }

    @Override
    public CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds(QueryByDealGroupIdRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return bpDealGroupQueryService.queryByDealGroupIds(request);
                    } catch (TException ex) {
                        Cat.logEvent(QUERY_CENTER, "remote-error");
                        ExUtils.compactIfTimoutEx(ex
                                , () -> LOGGER.error("queryByDealGroupIds-error,req:{}", JsonCodec.encode(request), ex)
                                , () -> LOGGER.error("queryByDealGroupIds-error,req:{},ex:{}", JsonCodec.encode(request), ExUtils.getStackTrace(ex)));
                        return null;
                    }
                }, bpDealGroupFetchPool)
                .thenApply(res -> {
                    if (Objects.isNull(res) || ResponseCodeEnum.SUCCESS.getCode() != res.getCode()) {
                        Cat.logEvent(QUERY_CENTER, "remote-fail");
                        String string = res == null ? "result null" : res.getMessage();
                        String errorField = res == null ? "result null" : JSON.toJSONString(res.getExceptionDealGroupField());
                        LOGGER.error("queryByDealGroupIds-fail,req:{},msg:{},errorField:{}", JsonCodec.encode(request), string, errorField);
                        return null;
                    }
                    return res.getData();
                });
    }

    @Override
    public CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds2(QueryByDealGroupIdRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return dealGroupQueryService2.queryByDealGroupIds(request);
                    } catch (TException ex) {
                        Cat.logEvent(QUERY_CENTER, "remote-error");
                        ExUtils.compactIfTimoutEx(ex
                                , () -> LOGGER.error("queryByDealGroupIds2-error,req:{}", JsonCodec.encode(request), ex)
                                , () -> LOGGER.error("queryByDealGroupIds2-error,req:{},ex:{}", JsonCodec.encode(request), ExUtils.getStackTrace(ex)));
                        return null;
                    }
                }, bpDealGroupFetchPool2)
                .thenApply(res -> {
                    if (Objects.isNull(res) || ResponseCodeEnum.SUCCESS.getCode() != res.getCode()) {
                        Cat.logEvent(QUERY_CENTER, "remote-fail");
                        String string = res == null ? "result null" : res.getMessage();
                        String errorField = res == null ? "result null" : JSON.toJSONString(res.getExceptionDealGroupField());
                        LOGGER.error("queryByDealGroupIds2-fail,req:{},msg:{},errorField:{}", JsonCodec.encode(request), string, errorField);
                        return null;
                    }
                    return res.getData();
                });
    }

    @Override
    public CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds3(QueryByDealGroupIdRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return dealGroupQueryService2.queryByDealGroupIds(request);
                    } catch (TException ex) {
                        Cat.logEvent(QUERY_CENTER, "remote-error");
                        ExUtils.compactIfTimoutEx(ex
                                , () -> LOGGER.error("queryByDealGroupIds2-error,req:{}", JsonCodec.encode(request), ex)
                                , () -> LOGGER.error("queryByDealGroupIds2-error,req:{},ex:{}", JsonCodec.encode(request), ExUtils.getStackTrace(ex)));
                        return null;
                    }
                }, bpDealGroupFetchPool3)
                .thenApply(res -> {
                    if (Objects.isNull(res) || ResponseCodeEnum.SUCCESS.getCode() != res.getCode()) {
                        Cat.logEvent(QUERY_CENTER, "remote-fail");
                        String string = res == null ? "result null" : res.getMessage();
                        String errorField = res == null ? "result null" : JSON.toJSONString(res.getExceptionDealGroupField());
                        LOGGER.error("queryByDealGroupIds3-fail,req:{},msg:{},errorField:{}", JsonCodec.encode(request), string, errorField);
                        return null;
                    }
                    return res.getData();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, Boolean>> batchVerifyProductMemberExclusive(BatchVerifyProductMemberExclusiveReqDTO reqDTO) {
        if (Objects.isNull(reqDTO)) {
            return CompletableFuture.completedFuture(null);
        }

        try {
            return AthenaInf.getRpcCompletableFuture(nibMemberProductQryThriftService.batchVerifyProductMemberExclusive(reqDTO)).thenApply(res -> {
                        if (Objects.isNull(res)) {
                            return Maps.newHashMap();
                        }
                        Map<Integer, Boolean> respDto = new HashMap<>();
                        res.getProductId2ExclusiveMap().forEach((key, value) -> {
                                    respDto.put(key.intValue(), value);
                                }
                        );
                        return respDto;
                    }
            );
        } catch (TException e) {
            LOGGER.error("queryByDealGroupIds error", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Map<Integer, List<String>>> getDealSubTitle(DealProductRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }

        try {
            return AthenaInf.getRpcCompletableFuture(dealProductBizQueryService.queryDealSubtitle(request)).thenApply(res -> {
                        if (Objects.isNull(res)) {
                            return Maps.newHashMap();
                        }
                        Map<Integer, List<String>> respDto = new HashMap<>();
                        res.getDeals().forEach((key) -> {
                                    respDto.put(key.getProductId(), key.getProductTags());
                                }
                        );
                        return respDto;
                    }
            );
        } catch (Exception e) {
            LOGGER.error("getDealSubTitle error", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Map<Long, DpPoiDTO>> batchQueryDpPoiDTO(DpPoiRequest request) {
        try {
            return AthenaInf.getRpcCompletableFuture(dpPoiService.findShopsByShopIds(request))
                    .exceptionally(e -> {
                        commonExceptionHandler("batchQueryDpPoiDTO", JsonCodec.encode(request), e);
                        return null;
                    })
                    .thenApply(dpPoiDTOs -> {
                        if (CollectionUtils.isEmpty(dpPoiDTOs)) {
                            return Maps.newHashMap();
                        }
                        return Converters.newMapByPropertyNameConverter("shopId", dpPoiDTOs);
                    });
        } catch (Exception e) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @Override
    public CompletableFuture<GetRelateExhibitInfoListResp> getRelatedExhibits(GetRelateExhibitInfoListReq req) {
        return AthenaInf.getRpcCompletableFuture(exhibitCustomerService.getRelatedExhibits(req))
                .exceptionally(ex -> {
                    commonExceptionHandler("getRelatedExhibits", JsonCodec.encode(req), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<GetBindedExhibitsResp> getBindedExhibits(GetBindedExhibitsReq req) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.getBindedExhibits(com.sankuai.mpmctexhibit.process.dto.dealgroup.GetBindedExhibitsReq)");
        return AthenaInf.getRpcCompletableFuture(dealGroupExhibitService.getBindedExhibits(req));
    }

    @Override
    public CompletableFuture<com.sankuai.mpmctexhibit.query.response.GetBindedExhibitsResp> getFastBindedExhibits(com.sankuai.mpmctexhibit.query.request.GetBindedExhibitsReq req) {
        return AthenaInf.getRpcCompletableFuture(fastDealGroupExhibitService.getBindedExhibits(req));
    }

    @Override
    public CompletableFuture<GetBindedExhibitsResp> getBindedExhibitCount(GetBindedExhibitsReq req) {
        return AthenaInf.getRpcCompletableFuture(dealGroupExhibitService.getBindedExhibitCount(req));
    }

    @Override
    public CompletableFuture<DealGroupAttributeDTO> getSingleDealDetailText(int dealGroupId) {
        return AthenaInf.getRpcCompletableFuture(dealGroupAttributeTextServiceFuture.get(dealGroupId)).exceptionally(ex -> {
            commonExceptionHandler("getSingleDealDetailText", JsonCodec.encode(dealGroupId), ex);
            return null;
        });
    }

    @Override
    public CompletableFuture<PriceResponse<Map<Integer, List<PriceDisplayDTO>>>> batchQueryPrice(BatchPriceRequest request) {
        return AthenaInf.getRpcCompletableFuture(priceDisplayService.batchQueryPrice(request)).exceptionally(ex -> {
            commonExceptionHandler("batchQueryPrice", JsonCodec.encode(request), ex);
            return null;
        });
    }

    @Override
    public CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> batchQueryPriceByLongShopId(BatchPriceRequest request) {
        DealProductReqMonitor.monitorPrice(request);
        return AthenaInf.getRpcCompletableFuture(priceDisplayService.batchQueryPriceByLongShopId(request)).exceptionally(ex -> {
            commonExceptionHandler("batchQueryPriceByLongShopId", JsonCodec.encode(request), ex);
            return null;
        });
    }

    @Override
    public CompletableFuture<PriceResponse<BatchTradePriceResponse>> batchQueryPriceForTrade(BatchTradePriceRequest request) {
        return AthenaInf.getRpcCompletableFuture(tradePriceService.batchQueryPriceForTrade(request)).exceptionally(ex -> {
            commonExceptionHandler("batchQueryPriceForTrade", JsonCodec.encode(request), ex);
            return null;
        });
    }

    /**
     * 获取团单的可用时间展示文案
     *
     * @param request 请求参数
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, DealGroupAvailableDateShowTextDTO>> batchGetDealGroupAvailableDateShowText(AvailableDateShowTextRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealGroupDetailService.multiGetDealGroupAvailableDateShowText(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchGetDealGroupAvailableDateShowText", JsonCodec.encode(request), ex);
                    return new HashMap<>();
                });
    }

    /**
     * 获取团单的详细可用时间展示文案
     *
     * @param dealGroupIDs 请求参数
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, DealGroupAvailableDateDTO>> batchGetDealGroupAvailableDatesWithDefault(List<Integer> dealGroupIDs, boolean needDisableDate) {
        return AthenaInf.getRpcCompletableFuture(dealGroupDetailService.multiGetDealGroupAvailableDatesWithDefault(dealGroupIDs, needDisableDate));
    }

    @Override
    public CompletableFuture<Map<Integer, String>> multiLoadBeautyMainDealTag(List<Integer> dpDealIds, int goodType) {
        return AthenaInf.getRpcCompletableFuture(beautySearchService.multiLoadBeautyMainDealTag(dpDealIds, goodType))
                .exceptionally(ex -> {
                    commonExceptionHandler("multiLoadBeautyMainDealTag", JsonCodec.encode(dpDealIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<BeautyTagItem>>> multiLoadAllBeautyDealTag(List<Integer> dpProductIds, int goodType) {
        return AthenaInf.getRpcCompletableFuture(beautySearchService.multiLoadAllBeautyDealTag(dpProductIds, goodType))
                .exceptionally(ex -> {
                    commonExceptionHandler("multiLoadAllBeautyDealTag", JsonCodec.encode(dpProductIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, String>> batchQueryBeautySpecialPromotionTag(List<Integer> dpProductIds, int goodType) {
        return AthenaInf.getRpcCompletableFuture(beautySearchService.multiLoadBeautyMainDealTag(dpProductIds, goodType))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQueryBeautySpecialPromotionTag", JsonCodec.encode(dpProductIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetDealSales(SalesDisplayRequest salesDisplayRequest) {
        return AthenaInf.getRpcCompletableFuture(shopSceneSalesDisplayService.multiGetSales(salesDisplayRequest))
                .exceptionally(ex -> {
                    commonExceptionHandlerCompactIfTimoutEx("batchGetDealSales", JsonCodec.encode(salesDisplayRequest), ex);
                    return new HashMap<>();
                }).thenApply(response -> org.apache.commons.lang3.ObjectUtils.defaultIfNull(response, Maps.newHashMap()));
    }

    @Override
    public CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetProductSceneSales(SalesDisplayRequest salesDisplayRequest) {
        return AthenaInf.getRpcCompletableFuture(productSceneSalesDisplayService.multiGetSales(salesDisplayRequest))
                .exceptionally(ex -> {
                    commonExceptionHandlerCompactIfTimoutEx("batchGetProductSceneSales", JsonCodec.encode(salesDisplayRequest), ex);
                    return new HashMap<>();
                }).thenApply(response -> org.apache.commons.lang3.ObjectUtils.defaultIfNull(response, Maps.newHashMap()));
    }

    @Override
    public CompletableFuture<Map<ProductParam, SalesDisplayDTO>> batchGetProductSceneSales(SalesDisplayRequest request, Execution execution) {
        if (LiveUtils.liveReqIndependentClient(execution)) {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Cat.logEvent(LIVE_RPC, "multiGetSales");
                    return productSceneSalesDisplayServiceLive.multiGetSales(request);
                } catch (Exception ex) {
                    Cat.logEvent(LIVE_RPC, "multiGetSales-error");
                    ExUtils.compactIfTimoutEx(ex
                            , () -> LOGGER.error("multiGetSales-error,req:{}", JsonCodec.encode(request), ex)
                            , () -> LOGGER.error("multiGetSales-error,req:{},ex:{}", JsonCodec.encode(request), ExUtils.getStackTrace(ex)));
                    return Maps.newHashMap();
                }
            }, liveRpcPool);
        }
        return batchGetProductSceneSales(request);
    }

    @Override
    public CompletableFuture<Map<SubjectParam, SubjectSalesDisplayDTO>> multiGetSubjectSales(SubjectSalesDisplayRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getSubjectParamList())) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return AthenaInf.getRpcCompletableFuture(subjectSalesDisplayService.multiGetSubjectSales(request))
                .exceptionally(ex -> {
                    commonExceptionHandlerCompactIfTimoutEx("multiGetSubjectSales", JsonCodec.encode(request), ex);
                    return null;
                }).thenApply(response -> {
                    if (response == null || MapUtils.isEmpty(response.getData())) {
                        return Maps.newHashMap();
                    }
                    return response.getData();
                });
    }

    @Override
    public CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> batchQuerySales(SalesDisplayQueryRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getSubjectParams())) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return AthenaInf.getRpcCompletableFuture(salesDisplayQueryService.batchQuerySales(request))
                .exceptionally(ex -> {
                    commonExceptionHandlerCompactIfTimoutEx("batchQuerySales", JsonCodec.encode(request), ex);
                    return null;
                }).thenApply(response -> {
                    if (response == null || MapUtils.isEmpty(response.getData())) {
                        return Maps.newHashMap();
                    }
                    return response.getData();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, ProductSaleDto>> multiQueryProductSaleForLong(Set<Integer> productGroupIds, int source, long dpShopId) {
        return AthenaInf.getRpcCompletableFuture(productSaleQueryService.multiQueryProductSaleL(productGroupIds, source, dpShopId));
    }

    @Override
    public CompletableFuture<BatchQueryDealActivityResponse> batchQueryDealActivity(BatchQueryDealActivityRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealActivityQueryService.batchQueryDealActivity(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQueryDealActivity", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<QueryActivityResponse> getPageByCategory(QueryActivityRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealActivityQueryService.queryPageByCategory(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("getPageByCategory", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<CheckDealItemRelationResponse> checkDealItemRelation(CheckDealItemRelationRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealListService.checkDealItemRelation(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("checkDealItemRelation", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<BeautyTagMap>> loadAll() {
        return AthenaInf.getRpcCompletableFuture(beautyTagMapService.loadAll());
    }

    @Override
    public CompletableFuture<Map<Long, ProductCategory>> getProductCategoryList(List<Long> productCategoryIds) {
        return AthenaInf.getRpcCompletableFuture(productFrontCategoryService.getProductCategoryList(productCategoryIds))
                .exceptionally(ex -> {
                    commonExceptionHandler("getProductCategoryList", JsonCodec.encode(productCategoryIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<GetUrlRespDTO> batchGetShopRecommendInfo(GetUrlReqDTO getUrlReqDTO) {
        if (getUrlReqDTO == null) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(barProductManagerService.batchGetShopRecommendInfo(getUrlReqDTO))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchGetShopRecommendInfo", JsonCodec.encode(getUrlReqDTO), ex);
                    return new GetUrlRespDTO();
                });
    }


    @Override
    public CompletableFuture<Response<Map<Product, List<PromoDisplayDTO>>>> batchQueryPromoDisplay(BatchQueryPromoDisplayRequest request) {
        return AthenaInf.getRpcCompletableFuture(promoDisplayServiceInSH.batchQueryPromoDisplay(request)).exceptionally(ex -> {
            commonExceptionHandler("batchQueryPromoDisplay", JsonCodec.encode(request), ex);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Integer, List<VoucherDTO>>> batchQueryDealGroupVoucher(List<Integer> dpDealGroupIDs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.batchQueryDealGroupVoucher(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(dealGroupVoucherQueryService.batchQueryDealGroupVoucher(dpDealGroupIDs));
    }

    @Override
    public CompletableFuture<UserAccountDTO> loadUserByDpId(long dpUserId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.loadUserByDpId(long)");
        return AthenaInf.getRpcCompletableFuture(userAccountService.loadById(dpUserId));
    }

    @Override
    public CompletableFuture<UserModel> getUserByMtUserId(long userId, UserFields fields) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.getUserByMtUserId(long,com.sankuai.wpt.user.retrieve.thrift.message.UserFields)");
        try {
            return AthenaInf.getRpcCompletableFuture(rpcUserRetrieveService.getUserById(userId, fields));
        } catch (TException e) {
            LOGGER.error("美团用户信息查询错误", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<PromotionResponse> getPromotions(PromotionRequest promotionRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.getPromotions(com.sankuai.nibmktproxy.queryclient.proxy.PromotionRequest)");
        try {
            return AthenaInf.getRpcCompletableFuture(promotionProxyService.getPromotions(promotionRequest));
        } catch (TException e) {
            LOGGER.error("查询优惠失败", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<CheckDealActivityRelationResponse> checkDealActivityRelation(CheckDealActivityRelationRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.checkDealActivityRelation(com.dianping.gmkt.activity.api.request.CheckDealActivityRelationRequest)");
        return AthenaInf.getRpcCompletableFuture(dealListService.checkDealActivityRelation(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("checkDealActivityRelation", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Integer> getDpCityIdByMtCityId(int mtCityId) {
        return AthenaInf.getRpcCompletableFuture(areaCommonService.getDpCityByMtCity(mtCityId));
    }

    @Override
    public CompletableFuture<List<MetaTagDTO>> queryByTagIds(List<Long> tagIds) {
        return AthenaInf.getRpcCompletableFuture(dealMetaTagQueryService.queryByTagIds(tagIds)).exceptionally(ex -> {
            commonExceptionHandler("queryByTagIds", JsonCodec.encode(tagIds), ex);
            return new ArrayList<>();
        });
    }

    @Override
    public CompletableFuture<CardResponse<Map<Integer, CardSimpleDTO>>> queryCardSimpleInfoByDealIds(QueryCardSimpInfoRequest request) {
        return AthenaInf.getRpcCompletableFuture(timesCardNavigationService.queryCardSimpleInfoByDealIds(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("queryCardSimpleInfoByDealIds", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<BestShopDTO> getDealGroupBestShop(BestShopReq req) {
        return AthenaInf.getRpcCompletableFuture(dealGroupBestShopQueryService.getDealGroupBestShop(req))
                .exceptionally(ex -> {
                    commonExceptionHandler("getDealGroupBestShop", JsonCodec.encode(req), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<CityInfoDTO>> findCities(List<Integer> cityIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.findCities(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(cityInfoService.findCities(cityIds))
                .exceptionally(ex -> {
                    commonExceptionHandler("findCities", JsonCodec.encode(cityIds), ex);
                    return new ArrayList<>();
                });
    }

    @Override
    public CompletableFuture<Map<Long, ShopCardDTO>> multiGetShopThemeDtoForLong(ShopThemePlanRequest request) {
        DealProductReqMonitor.monitorShopTheme(request);
        return AthenaInf.getRpcCompletableFuture(dzThemeShopService.queryShopTheme(request))
                .exceptionally(ex -> {
                    commonExceptionHandler("multiGetShopThemeDtoForLong", JsonCodec.encode(request), ex);
                    return null;
                }).thenApply(res -> {
                    if (res == null) {
                        return new HashMap<>();
                    }
                    return res.getShopCardDTOMap();
                });
    }

    @Override
    public CompletableFuture<Map<Long, ShopCardDTO>> multiGetShopThemeDtoForLong(ShopThemePlanRequest request, Execution execution) {
        if (LiveUtils.liveReqIndependentClient(execution)) {
            DealProductReqMonitor.monitorShopTheme(request);
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Cat.logEvent(LIVE_RPC, "dzThemeShop");
                    ShopThemeResponse response = dzThemeShopServiceLive.queryShopTheme(request);
                    if (response == null){
                        return Maps.newHashMap();
                    }
                    return response.getShopCardDTOMap();
                } catch (Exception ex) {
                    Cat.logEvent(LIVE_RPC, "dzThemeShop-error");
                    LOGGER.error("dzThemeShop-error,req:{}", JsonCodec.encode(request), ex);
                    return Maps.newHashMap();
                }
            }, liveRpcPool);
        } else {
            return multiGetShopThemeDtoForLong(request);
        }
    }

    @Override
    public CompletableFuture<Map<Integer, DealGroupDetailDTO>> multiGetDealGroupDetails(List<Integer> dealGroupIds) {
        return AthenaInf.getRpcCompletableFuture(dealGroupDetailService.multiGetDealGroupDetails(dealGroupIds));
    }

    @Override
    public CompletableFuture<BabyHotSaleTgDTO> getOnLineHotSaleByCityIdAndDealId(int dpCityId, int dpDealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.getOnLineHotSaleByCityIdAndDealId(int,int)");
        return AthenaInf.getRpcCompletableFuture(babyHotSaleTgService.getOnLineHotSaleByCityIdAndDealId(dpCityId, dpDealId));
    }

    @Override
    public CompletableFuture<Pair<Long, RentalCarDepositRuleDTO>> queryRentCarShopDepositRule(Long mtShopId) {
        if (mtShopId == null) {
            return CompletableFuture.completedFuture(null);
        }
        DepositRuleQueryReqDTO reqDTO = new DepositRuleQueryReqDTO();
        reqDTO.setMtShopId(mtShopId);
        return AthenaInf.getRpcCompletableFuture(rentalCarToCRemoteService.queryDepositRule(reqDTO))
                .thenApply(o -> {
                    if (o == null) {
                        return Pair.of(mtShopId, null);
                    }
                    return Pair.of(mtShopId, o.getRentalCarDepositRuleDTO());
                });
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> queryContentIdsByDpDealGroupIdsAndContentType(List<Long> dpDealGroupIds, int contentType) {
        return AthenaInf.getRpcCompletableFuture(dealGroupContentRelationQueryService.queryContentIdsByDpDealGroupIdsAndContentType(dpDealGroupIds, contentType))
                .exceptionally(ex -> {
                    commonExceptionHandler("queryContentIdsByDpDealGroupIdsAndContentType", JsonCodec.encode(dpDealGroupIds), ex);
                    return new HashMap<>();
                });
    }

    @Override
    public CompletableFuture<QueryContentDetailRespDTO> queryContentDetails(QueryContentDetailReqDTO reqDTO) {
        try {
            return AthenaInf.getRpcCompletableFuture(contentAggregate2CService.queryContentDetails(reqDTO));
        } catch (TException e) {
            LOGGER.error("团单关联款式查询错误", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private void commonExceptionHandler(String methodName, String paramsStr, Throwable throwable) {
        LOGGER.error(XMDLogFormat.build()
                .putTag("scene", "FacadeService")
                .putTag("method", methodName)
                .message(String.format("%s error, params: %s", methodName, paramsStr)), throwable);
    }

    private void commonExceptionHandlerCompactIfTimoutEx(String methodName, String paramsStr, Throwable throwable) {
        ExUtils.compactIfTimoutEx(LOGGER, "FacadeService", methodName, paramsStr, throwable);
    }

    @Override
    public CompletableFuture<RecommendResult<RecommendDTO>> getDealRecommendResult(RecommendParameters recommendParameters) {
        return AthenaInf.getRpcCompletableFuture(recommendService.recommend(recommendParameters, RecommendDTO.class))
                .exceptionally(ex -> {
                    commonExceptionHandler("recommendService.recommend", JsonCodec.encode(recommendParameters), ex);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getResult() == null) {
                        return null;
                    }
                    return response.getResult();
                });
    }

    @Override
    public CompletableFuture<List<TechnicianDetailDTO>> queryRelateTech(int dpDealId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dealtheme.core.nr.atom.impl.FacadeServiceImpl.queryRelateTech(int)");
        return AthenaInf.getRpcCompletableFuture(technicianGrouponService.queryMedicalByGroupId(dpDealId))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQueryRelateTech", String.valueOf(dpDealId), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ServiceResponse<Map<String, PictureRespDTO>>> batchQuerySmartHeadPic(PictureBatchQueryReqDTO pictureBatchQueryReqDTO) {
        return AthenaInf.getRpcCompletableFuture(smartPictureQueryService.batchGetCuttingResult(pictureBatchQueryReqDTO))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchGetCuttingResult", JsonCodec.encode(pictureBatchQueryReqDTO), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Long, DealSkuSummaryDTO>> batchQuerySummary(SkuOptionBatchRequest batchRequest) {
        return AthenaInf.getRpcCompletableFuture(dealSkuService.batchQuerySummary(batchRequest))
                .exceptionally(ex -> {
                    commonExceptionHandler("batchQuerySummary", JsonCodec.encode(batchRequest), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<BatchQueryResvBookingTagRespDTO> batchQueryResvBookingTag(BatchQueryResvBookingTagReqDTO reqDTO) {
        try {
            BatchQueryResvBookingTagRespDTO respDTO = resvQueryService.batchQueryResvBookingTagFromCache(reqDTO);
            return CompletableFuture.completedFuture(respDTO);
        } catch (Exception e) {
            commonExceptionHandler("queryByDealGroupIds", JsonCodec.encode(reqDTO), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<LeadsCountRespDTO> queryLeadsCount(LeadsCountADTO reqDTO) {
        try {
            LeadsCountRespDTO respDTO = newLeadsCountService.queryCountByCache(reqDTO);
            return CompletableFuture.completedFuture(respDTO);
        } catch (Exception e) {
            commonExceptionHandler("queryLeadsCounts", JsonCodec.encode(reqDTO), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<QueryLeadsSalesRespDTO> queryLeadsSales(QueryLeadsSalesReqDTO reqDTO) {
        try {
            QueryLeadsSalesRespDTO respDTO = newLeadsCountService.queryLeadsSales(reqDTO);
            return CompletableFuture.completedFuture(respDTO);
        } catch (Exception e) {
            commonExceptionHandler("queryLeadsCounts", JsonCodec.encode(reqDTO), e);
            return CompletableFuture.completedFuture(null);
        }
    }


    @Override
    public CompletableFuture<RankingResult> queryDealFirstRank(RankLabelDataRequest rankLabelDataRequest) {
        return AthenaInf.getRpcCompletableFuture(rankLabelQuery.queryProductFirstRank(rankLabelDataRequest))
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getResult() == null) {
                        return null;
                    }
                    return response.getResult();
                }).exceptionally(ex -> {
                    commonExceptionHandler("queryDealFirstRank", JsonCodec.encode(rankLabelDataRequest), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryProductGuarantee(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request) {
        try {
            return AthenaInf.getRpcCompletableFuture(guaranteeQueryService.batchQueryGuaranteeTag(sessionContext, request))
                    .thenApply(response -> {
                        if (response == null || response.isNotSuccess() || CollectionUtils.isEmpty(response.getData())) {
                            return null;
                        }
                        return response.getData();
                    }).exceptionally(ex -> {
                        commonExceptionHandler("batchQueryProductGuarantee", JsonCodec.encode(request), ex);
                        return null;
                    });
        } catch (TException e) {
            commonExceptionHandler("batchQueryProductGuarantee", JsonCodec.encode(request), e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Map<Long, Boolean>> batchCheckInsuranceAvailable(List<ReadInsuranceReqDTO> reqDTOS) {
        return AthenaInf.getRpcCompletableFuture(universalInsuranceService.batchCheckInsuranceAvailable(reqDTOS))
                .thenApply(response -> {
                    if (MapUtils.isEmpty(response)) {
                        return null;
                    }
                    return response;
                }).exceptionally(ex -> {
                    commonExceptionHandler("batchCheckInsuranceAvailable", JsonCodec.encode(reqDTOS), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<YlxPackageSearchResp> searchInsurance(YlxPackageSearchRequest request, SignInfo signInfo) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                YlxPackageSearchResp response = ylxPackageSearchService.search(request, signInfo);
                if (response == null || response.getStatus().getCode() != 0) {
                    //没签约也会抛异常, 但理论上没签约不会走到这个接口，被batchCheckInsuranceAvailable拦截掉了
                    //也就是只要查询这个接口，都是已签约, 不应该查不到结果
                    Cat.logError(String.format("保险套餐查询接口异常, request:%s", JsonCodec.encode(request)), new ParamException(String.format("response:%s", JsonCodec.encode(response))));
                    return null;
                }
                return response;
            } catch (Exception e) {
                commonExceptionHandler("searchInsurance", JsonCodec.encode(request), e);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<Map<Integer, MemberDiscountInfoDTO>> batchQueryMerchantMemberProducts(BatchGetGrouponMemberDiscountInfoReqDTO request) {
        try {
            CompletableFuture<BatchGetGrouponMemberDiscountInfoRespDTO> memberDiscountFuture = AthenaInf.getRpcCompletableFuture(nibMemberProductQryThriftService.batchGetGrouponMemberDiscountInfo(request));
            return memberDiscountFuture
                    .thenApply(response ->{
                        if(null == response || null == response.getCommonResp() || CommonRespCodeEnum.CODE_200.code() != response.getCommonResp().getCode() ){
                            return Collections.emptyMap();
                        }
                        Map<Integer, MemberDiscountInfoDTO> mtGrouponId2DiscountInfoMap = new HashMap<>();
                        response.getMtGrouponId2DiscountInfoMap().forEach((key,value) ->{
                            mtGrouponId2DiscountInfoMap.put(key.intValue(),value);
                        }) ;

                        return mtGrouponId2DiscountInfoMap;
                    });
        } catch (TException e) {
            LOGGER.error(String.format("multiGetMerchantMemberProducts.batchGetGrouponMemberDiscountInfo error,req:%s,msg:%s",JsonCodec.encode(request),e.getMessage()),e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Long> loadDpUserId2MtUserId(Long dpUserId) {
        if(null == dpUserId){
            return CompletableFuture.completedFuture(null);
        }

        try {
            return AthenaInf.getRpcCompletableFuture(userMergeQueryService.getVirtualBindByDpUserId(dpUserId))
                    .thenApply(bindRelationResp ->{
                        if(null == bindRelationResp || !bindRelationResp.isSuccess() || null == bindRelationResp.getData() || null == bindRelationResp.getData().getMtUserId()){
                            return null;
                        }
                        return bindRelationResp.getData().mtUserId.getId();
                    });
        } catch (TException e) {
            LOGGER.error(String.format("loadDpUserId2MtUserId error,dpUserId:%s,msg:%s",dpUserId,e.getMessage()),e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Long> getMtRealUserIdByDpUserId(long dpUserId) {
        if(dpUserId <= 0){
            return CompletableFuture.completedFuture(0L);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(userMergeQueryService.getFlattedBindAggregateByDpUserId(dpUserId))
                    .thenApply(res -> {
                        if (res == null || !res.isSuccess() || res.getFlattedAggregateData() == null) {
                            return null;
                        }
                        return res.getFlattedAggregateData().getMtRealUserId();
                    }).exceptionally(ex -> {
                        LOGGER.error(String.format("getUserInfoByDpUserId error,dpUserId:%s,msg:%s",dpUserId, ex.getMessage()), ex);
                        return null;
                    });
        } catch (Exception e) {
            LOGGER.error(String.format("getUserInfoByDpUserId error,dpUserId:%s,msg:%s",dpUserId, e.getMessage()), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryProductGuarantee(SessionContextDTO sessionContext,
                                                                                     BatchQueryGuaranteeTagRequest request,
                                                                                     Execution execution) {
        if (LiveUtils.liveReqIndependentClient(execution)) {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Cat.logEvent(LIVE_RPC, "batchQueryProductGuarantee");
                    com.sankuai.nib.price.operation.api.common.response.Response<List<ObjectGuaranteeTagDTO>> response =
                            guaranteeQueryServiceLive.batchQueryGuaranteeTag(sessionContext, request);
                    if (response == null || response.isNotSuccess() || CollectionUtils.isEmpty(response.getData())) {
                        return Lists.newArrayList();
                    }
                    return response.getData();
                } catch (Exception ex) {
                    Cat.logEvent(LIVE_RPC, "batchQueryProductGuarantee-error");
                    LOGGER.error("batchQueryProductGuarantee-error,request:{}", JsonCodec.encode(request), ex);
                    return Lists.newArrayList();
                }
            }, liveRpcPool);
        } else {
            return batchQueryProductGuarantee(sessionContext, request);
        }
    }

    @Override
    public CompletableFuture<QueryStockRespDTO> queryResvStatus(QueryStockReqDTO queryStockReqDTO){
        return CompletableFuture.supplyAsync(() -> {
            try {
                return resvQueryService.queryStock(queryStockReqDTO);
            } catch (Exception e) {
                commonExceptionHandler("queryResvStatus", JsonCodec.encode(queryStockReqDTO), e);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<BatchQueryResvBookingTagRespDTO> batchQueryResvBookingTagV1(BatchQueryResvBookingTagReqDTO reqDTO) {
        try {
            BatchQueryResvBookingTagRespDTO respDTO = resvQueryService.batchQueryResvBookingTag(reqDTO);
            return CompletableFuture.completedFuture(respDTO);
        } catch (Exception e) {
            commonExceptionHandler("batchQueryResvBookingTagV1", JsonCodec.encode(reqDTO), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<LoadDealRelatedCaseListRespDTO> queryDealRelatedCase(LoadContentByExternalBizIdDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getSubBizType())) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return dealDetailPageGWService.loadDealRelatedCaseList(request);
                    } catch (Exception ex) {
                        Cat.logEvent(QUERY_CENTER, "remote-error");
                        commonExceptionHandler("queryDealRelatedCase", JsonCodec.encode(request), ex);
                        return null;
                    }
                }, bpDealGroupFetchPool)
                .thenApply(res -> {
                    if (Objects.isNull(res) || !res.isSuccess()) {
                        Cat.logEvent(QUERY_CENTER, "remote-fail");
                        String string = res == null ? "result null" : res.getCommonResp().getMsg();
                        String errorField = res == null ? "result null" : JSON.toJSONString(res.getCommonResp().getMsg());
                        LOGGER.error("queryDealRelatedCase-fail,req:{},msg:{},errorField:{}", JsonCodec.encode(request), string, errorField);
                        return null;
                    }
                    return res;
                });
    }

    @Override
    public CompletableFuture<List<PlanDTO>> batchQueryPlanList(BatchQueryPlanListReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                BatchQueryPlanListRespDTO planListRespDTO = contentProcessService.batchQueryPlanList(reqDTO);
                if (planListRespDTO == null || !planListRespDTO.isSuccess() || CollectionUtils.isEmpty(planListRespDTO.getPlanList())) {
                    return Collections.emptyList();
                }
                return planListRespDTO.getPlanList();
            } catch (Exception e) {
                Cat.logEvent(LIVE_RPC, "batchQueryPlanList-error");
                LOGGER.error("batchQueryPlanList-error,reqDTO:{}", JSON.toJSONString(reqDTO),e);
                return Collections.emptyList();
            }
        }, liveRpcPool);
    }


    @Override
    public CompletableFuture<BatchQueryLatestStockRespDTO> batchQueryLatestStock(BatchQueryLatestStockReqDTO latestStockReqDTO) {
        if (Objects.isNull(latestStockReqDTO)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return resvQueryService.batchQueryLatestStock(latestStockReqDTO);
            } catch (Exception e) {
                Cat.logEvent("", "batchQueryLatestStock-error");
                LOGGER.error("batchQueryLatestStock-error,reqDTO:{}", JSON.toJSONString(latestStockReqDTO),e);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<Long> getMtByDpPoiIdL(long dpPoiId) {
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByDpIdL(dpPoiId)).thenApply(response->{
                if (response == null) {
                    return 0L;
                }
                return Optional.ofNullable(response.getMtId()).orElse(0L);
            }).exceptionally(ex -> {
                commonExceptionHandler("getMtByDpPoiIdL", String.valueOf(dpPoiId), ex);
                return 0L;
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public CompletableFuture<SearchDetailResponseDTO> searchDetail(String searchScene, List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                SearchRequestDTO reqDTO = SearchRequestDTOBuilder.builder(searchScene)
                        .start(0)
                        .limit(list.size())
                        .appendLongListParam("infoContentId", MultiParam.of(list))
                        .build();
                return contentSearchService.searchDetail(reqDTO);
            } catch (Exception e) {
                LOGGER.error("contentSearchService-searchDetail-error", e);
                return null;
            }
        }, dealCommonPool).thenApply(response -> response);
    }

    public CompletableFuture<PlayExecuteResponse> batchQueryDealScenePlay(ScenePlayExecuteRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return playCenterService.sceneExecutePlay(request);
            } catch (Exception e) {
                LOGGER.error("batchQueryDealScenePlay-error", e);
                return null;
            }
        }, dealCommonPool).thenApply(response -> response);
    }

    @Override
    public CompletableFuture<SubjectTagBatchJudgeResponse> batchQueryDealSubjectTags(SubjectTagBatchJudgeRequest request) {
        List<CompletableFuture<SubjectTagBatchJudgeResponse>> batchResponse = getBatchSubjectTagBatchJudge(request);
        CompletableFuture<List<SubjectTagBatchJudgeResponse>> flipCf = Async.each(batchResponse);
        return flipCf.thenApply(this::merge2Single);
    }

    @Override
    public CompletableFuture<BatchQueryLeadsInfoRespDTO> batchQueryDealLeadsInfo(BatchQueryLeadsInfoReqDTO request) {
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return leadsQueryService.batchQueryLeadsInfo(request);
                    } catch (Exception ex) {
                        Cat.logEvent(QUERY_CENTER, "remote-error");
                        commonExceptionHandler("batchQueryDealLeadsInfo", JsonCodec.encode(request), ex);
                        return null;
                    }
                }, dealCommonPool)
                .thenApply(res -> {
                    if (Objects.isNull(res) || !res.isSuccess()) {
                        Cat.logEvent(QUERY_CENTER, "remote-fail");
                        String string = res == null ? "result null" : res.getCommonResp().getMsg();
                        String errorField = res == null ? "result null" : JSON.toJSONString(res.getCommonResp().getMsg());
                        LOGGER.error("batchQueryDealLeadsInfo-fail,req:{},msg:{},errorField:{}", JsonCodec.encode(request), string, errorField);
                        return null;
                    }
                    return res;
                });
    }

    @Override
    public CompletableFuture<List<com.dianping.tpfun.product.api.sku.model.Product>> mGetBaseProductByIds(List<Integer> productIds) {
        return AthenaInf.getRpcCompletableFuture(productService.mGetBaseProductByIds(productIds))
                .exceptionally(ex -> {
                    commonExceptionHandler("mGetBaseProductByIds", JsonCodec.encode(productIds), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<Integer>>> getItemIdsByProductIds(List<Integer> productIds) {
        return AthenaInf.getRpcCompletableFuture(productService.getItemIdsByProductIds(productIds))
                .exceptionally(ex -> {
                    commonExceptionHandler("getItemIdsByProductIds", JsonCodec.encode(productIds), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, ProductItem>> getItemsById(GetProductItemRequest getProductItemRequest) {
        return AthenaInf.getRpcCompletableFuture(productService.getItemsById(getProductItemRequest))
                .exceptionally(ex -> {
                    commonExceptionHandler("getItemsById", JsonCodec.encode(getProductItemRequest), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<SaleStatisticsDTO>> queryFunSaleStatistics(SaleStatisticsQueryDTO saleStatisticsQueryDTO) {
        return AthenaInf.getRpcCompletableFuture(saleStatisticsService.queryFunSaleStatistics(saleStatisticsQueryDTO))
                .thenApply(res -> {
                    if (res == null || !res.isSuccess()) {
                        return null;
                    }
                    return res.getResult();
                })
                .exceptionally(ex -> {
                    commonExceptionHandler("queryFunSaleStatistics", JsonCodec.encode(saleStatisticsQueryDTO), ex);
                    return null;
                });
    }

    @Override
    public CompletableFuture<QueryDigestResponseDTO> queryDigest(QueryDigestRequestDTO queryDigestRequestDTO) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return digestQueryService.queryDigest(queryDigestRequestDTO);
            } catch (Exception ex) {
                LOGGER.error("queryDigest-error", ex);
                commonExceptionHandler("queryDigest", JsonCodec.encode(queryDigestRequestDTO), ex);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<List<Long>> queryWashingWhiteCustomerList(WashingWhiteCustomerListRequest washingWhiteCustomerListRequest) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return reserveConfigQueryService.queryWashingWhiteCustomerList(washingWhiteCustomerListRequest);
            } catch (Exception ex) {
                LOGGER.error("queryWashingWhiteCustomerList-error", ex);
                commonExceptionHandler("queryWashingWhiteCustomerList", JsonCodec.encode(washingWhiteCustomerListRequest), ex);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<TechProductUrlMultiDTO> queryTechProductUrl(TechProductUrlBatchQuery request) {
        return AthenaInf.getRpcCompletableFuture(techProductQueryService.queryTechProductUrl(request))
                .thenApply(res -> {
                    if (!res.respSuccess() || res.getData() == null) {
                        return null;
                    }
                    return res.getData();
                })
                .exceptionally(ex -> {
                    commonExceptionHandler("queryFunSaleStatistics", JsonCodec.encode(request), ex);
                    return null;
                });
    }

    private SubjectTagBatchJudgeResponse merge2Single(List<SubjectTagBatchJudgeResponse> responselist) {
        if (CollectionUtils.isEmpty(responselist)) {
            return null;
        }
        SubjectTagBatchJudgeResponse finalRes = new SubjectTagBatchJudgeResponse();
        Map<Long, Map<Long, Boolean>> finalMap = Maps.newHashMap();
        String finalMessage = StringUtils.EMPTY;
        for (SubjectTagBatchJudgeResponse perResponse : responselist) {
            if (perResponse == null || !perResponse.isSuccess()) {
                continue;
            }
            finalMap.putAll(perResponse.getJudgeResult());
            if (StringUtils.isNotEmpty(perResponse.getErrorMessage())) {
                finalMessage = finalMessage.concat(perResponse.getErrorMessage());
            }
        }
        finalRes.setJudgeResult(finalMap);
        finalRes.setSuccess(MapUtils.isNotEmpty(finalMap));
        finalRes.setErrorMessage(finalMessage);
        return finalRes;
    }

    private List<CompletableFuture<SubjectTagBatchJudgeResponse>> getBatchSubjectTagBatchJudge(SubjectTagBatchJudgeRequest request) {
        List<CompletableFuture<SubjectTagBatchJudgeResponse>> list = Lists.newArrayList();
        List<List<Long>> subjectIdsList = Lists.partition(request.getSubjectIds(), 50);
        for (List<Long> subjectIds : subjectIdsList) {
            SubjectTagBatchJudgeRequest perRequest = new SubjectTagBatchJudgeRequest();
            perRequest.setSubjectIds(subjectIds);
            perRequest.setSubjectType(request.getSubjectType());
            perRequest.setTagIds(request.getTagIds());
            CompletableFuture<SubjectTagBatchJudgeResponse> rpcCompletableFuture = AthenaInf.getRpcCompletableFuture(subjectTagJudgeService.batchJudgeSubjectTag(perRequest))
                .exceptionally(ex -> {
                    commonExceptionHandler("getBatchSubjectTagBatchJudge", JsonCodec.encode(request), ex);
                    return null;
                });
            list.add(rpcCompletableFuture);
        }
        return list;
    }

    public CompletableFuture<ActivityDetailDTO> queryOnlineActivity(ActivityProductQueryRequest request){
        return AthenaInf.getRpcCompletableFuture(activityShelfQueryService.queryOnlineActivity(request))
                .thenApply(response -> {
                    if (response == null || !response.isSuccess()) {
                        LOGGER.warn("queryOnlineActivity{}", JsonCodec.encode(request));
                        return null;
                    }
                    return response.getContent();
                });
    }

    @Override
    public CompletableFuture<ActivityShelfToCDTO> queryProductListByShopIdToC(ProductQueryByShopIdToCRequest request) {
        return AthenaInf.getRpcCompletableFuture(activityShelfQueryService.queryProductListByShopIdToC(request))
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getContent() == null) {
                        LOGGER.warn("queryProductListByShopIdToC{}", JsonCodec.encode(request));
                        return new ActivityShelfToCDTO();
                    }
                    return response.getContent();
                });
    }

    public CompletableFuture<SubjectConfigDTOList> subjectConfigQuery(SubjectConfigQueryRequest request) {
        return CompletableFuture.supplyAsync(() -> subjectConfigManageService.subjectConfigQuery(request), dealCommonPool)
                .thenApply(response -> response.isSuccess() ? response.getContent() : null)
                .exceptionally(e -> {
                    commonExceptionHandler("subjectConfigQuery", JsonCodec.encode(request), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Long, Boolean>> batchQueryShopLicenseAuthSuccessStatus(List<Long> shopIds,LicenseCategoryEnum licenseCategoryEnum) {
        if(CollectionUtils.isEmpty(shopIds)){
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        CompletableFuture<Map<Long, Boolean>> shopLicenseAuthStatusCf = AthenaInf.getRpcCompletableFuture(shopLicenseService.batchQueryBasicShopLicensesNew(shopIds, licenseCategoryEnum.getCategory()))
                .thenApply(response->processShopLicensesResponse(shopIds,response))
                .exceptionally(ex -> handleExceptionForShopLicenses(shopIds, ex));
        return shopLicenseAuthStatusCf;
    }

    private Map<Long, Boolean> processShopLicensesResponse(List<Long> shopIds, List<ShopLicenseNewDTO> response) {
        if (CollectionUtils.isEmpty(response)) {
            return new HashMap<>();
        }
        return buildShopLinceseAuthStatusMap(shopIds, response);
    }

    private Map<Long, Boolean> handleExceptionForShopLicenses(List<Long> shopIds, Throwable ex) {
        commonExceptionHandler("batchQueryShopLicenseAuthSuccessStatus", JsonCodec.encode(shopIds), ex);
        // 异常兜底默认门店资质符合要求
        return shopIds.stream().collect(Collectors.toMap(Function.identity(), shopId -> Boolean.TRUE));
    }

    private Map<Long, Boolean> buildShopLinceseAuthStatusMap(List<Long> shopIds,List<ShopLicenseNewDTO> shopLicenseList) {
        Map<Long, ShopLicenseNewDTO> shopLicenseMap = shopLicenseList.stream()
                .collect(Collectors.toMap(ShopLicenseNewDTO::getShopID, Function.identity(), (existing, replacement) -> existing));
        Map<Long, Boolean> result = shopIds.stream()
                .collect(Collectors.toMap(Function.identity(), shopId -> isLinceseAuthSusscess(shopLicenseMap.get(shopId))));
        return result;
    }

    private boolean isLinceseAuthSusscess(ShopLicenseNewDTO shopLicenseNewDTO){
        return shopLicenseNewDTO != null && shopLicenseNewDTO.getAuthStatus() == AuthStatusEnum.AUTH_SUCCESS.getCode();
    }

    @Override
    public CompletableFuture<Map<String, List<TagValueDTO>>> queryTagValueMetaInfo(int bizLine, String entityName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                TagValueRequestDTO reqDTO = new TagValueRequestDTO();
                reqDTO.setBizLine(bizLine);
                reqDTO.setEntityName(entityName);
                return metaInfoService.queryTagValueMetaInfo(reqDTO);
            } catch (Exception e) {
                LOGGER.error("metaInfoService-queryTagValueMetaInfo-error", e);
                return null;
            }
        }, dealCommonPool).thenApply(response -> {
            if (response == null || !Objects.equals(response.getCode(), CommonRespCodeEnum.CODE_200.code())) {
                return null;
            }
            return response.getTagValuePathMap();
        });
    }

    @Override
    public CompletableFuture<DealGroupEarliestReserveTimeResp> queryDealGroupEarliestReserveTimeInfo(DealGroupEarliestReserveTimeReq request) {
        try {
            return AthenaInf.getRpcCompletableFuture(lifeFulfillmentInfoQueryService.queryDealGroupEarliestReserveTimeInfo(request)).exceptionally(ex -> {
                commonExceptionHandler("queryByKey", JsonCodec.encode(request), ex);
                return null;
            });
        } catch (TException e) {
            LOGGER.error("queryDealGroupEarliestReserveTimeInfo-error", e);
            return null;
        }
    }

    @Override
    public CompletableFuture<QueryResultSync> queryByKey(KeyQueryParamSync request) {
        try {
            return AthenaInf.getRpcCompletableFuture(queryDataSyncService.queryByKey(request))
                    .exceptionally(ex -> {
                        commonExceptionHandler("queryByKey", JsonCodec.encode(request), ex);
                        return null;
                    });
        } catch (TException e) {
            LOGGER.error("queryDataSyncService.queryByKey-error", e);
            return null;
        }
    }

    @Override
    public CompletableFuture<List<MiniprogramJumpLinkDTO>> batchGetMiniProgramJumpLink(BatchMiniprogramJumpLinkRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<MiniprogramJumpLinkDTO>> response = miniprogramLinkService.batchGetJumpLink(request);
                if (response == null || response.getData() == null) {
                    return null;
                }
                return response.getData();
            } catch (Exception ex) {
                LOGGER.error("batchGetMiniProgramJumpLink-error", ex);
                commonExceptionHandler("batchGetMiniProgramJumpLink", JsonCodec.encode(request), ex);
                return null;
            }
        }, dealCommonPool);
    }

    @Override
    public CompletableFuture<Map<Integer, List<ProductUrlDTO>>> getProductUrls(GetProductUrlRequest request) {
        return AthenaInf.getRpcCompletableFuture(productDetailService.getProductUrls(request)).exceptionally(e -> {
            LOGGER.error(XMDLogFormat.build()
                    .putTag("scene", "themeAtomService")
                    .putTag("method", "getProductUrls")
                    .message(String.format("getProductUrls error, request : %s", JsonCodec.encode(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Long, List<MetaTagTreeByProductDTO>>> batchGetProductTreeTagFromCache(List<Long> productIds, int productType) {
        return AthenaInf.getRpcCompletableFuture(tagQueryService.batchQueryProductTreeTagFromCache(productIds, productType)).exceptionally(e -> {
            LOGGER.error(XMDLogFormat.build()
                    .putTag("scene", "themeAtomService")
                    .putTag("method", "batchGetProductTreeTagFromCache")
                    .message(String.format("batchGetProductTreeTagFromCache error, productType : %d, productIds: %s", productType, JsonCodec.encode(productIds))));
            return Maps.newHashMap();
        });
    }
}
