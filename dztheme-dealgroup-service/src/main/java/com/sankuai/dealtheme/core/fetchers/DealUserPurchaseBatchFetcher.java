package com.sankuai.dealtheme.core.fetchers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.fetchers.BatchFetcher;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.dealtheme.core.utils.UserUtils;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户团单购买次数批量获取器
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Fetcher(name = "dealUserPurchase")
public class DealUserPurchaseBatchFetcher implements BatchFetcher<DealModel, DealUserPurchaseInfoModel> {

    @Autowired
    private FacadeService facadeService;

    @Override
    public CompletableFuture<Map<DealModel, DealUserPurchaseInfoModel>> batchFetch(
            List<DealModel> dealModels,
            @Param("execution") Execution execution) {
        
        if (CollectionUtils.isEmpty(dealModels)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取当前用户ID
        Long currentUserId = UserUtils.getCurrentUserId(execution);
        if (currentUserId == null || currentUserId <= 0) {
            log.warn("DealUserPurchaseBatchFetcher: 无法获取当前用户ID");
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取团单ID列表
        List<Integer> dealIds = dealModels.stream()
                .map(DealModel::getDealId)
                .collect(Collectors.toList());

        // 先转换用户ID（美团用户ID转点评用户ID）
        return facadeService.getRealBindByMtUserId(currentUserId)
                .thenCompose(dpUserId -> {
                    if (dpUserId == null || dpUserId <= 0) {
                        log.warn("DealUserPurchaseBatchFetcher: 无法转换用户ID, mtUserId:{}", currentUserId);
                        return CompletableFuture.completedFuture(Maps.newHashMap());
                    }

                    // 搜索用户订单信息（180天内，不区分平台）
                    return facadeService.searchOrderByUserId(dpUserId, 180)
                            .thenApply(orderSearchResponse -> {
                                return buildUserPurchaseInfoMap(dealModels, dealIds, orderSearchResponse);
                            });
                })
                .exceptionally(ex -> {
                    log.error("DealUserPurchaseBatchFetcher: 获取用户购买信息失败, userId:{}", currentUserId, ex);
                    return Maps.newHashMap();
                });
    }

    /**
     * 构建用户购买信息映射
     */
    private Map<DealModel, DealUserPurchaseInfoModel> buildUserPurchaseInfoMap(
            List<DealModel> dealModels,
            List<Integer> dealIds,
            OrderSearchResponse orderSearchResponse) {

        Map<DealModel, DealUserPurchaseInfoModel> resultMap = Maps.newHashMap();

        // 统计每个团单的购买次数
        Map<Integer, Integer> dealPurchaseCountMap = Maps.newHashMap();

        // 处理订单搜索结果
        if (orderSearchResponse != null && CollectionUtils.isNotEmpty(orderSearchResponse.getData())) {
            processOrderSearchData(orderSearchResponse.getData(), dealIds, dealPurchaseCountMap);
        }

        // 为每个团单构建购买信息模型
        for (DealModel dealModel : dealModels) {
            Integer dealId = dealModel.getDealId();
            Integer purchaseCount = dealPurchaseCountMap.getOrDefault(dealId, 0);

            DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
            purchaseInfo.setDealId(dealId);
            purchaseInfo.setPurchaseCount(purchaseCount);

            resultMap.put(dealModel, purchaseInfo);
        }

        return resultMap;
    }

    /**
     * 处理订单搜索数据，统计购买次数
     */
    private void processOrderSearchData(List<Map<String, String>> orderData, List<Integer> dealIds, Map<Integer, Integer> dealPurchaseCountMap) {
        try {
            if (CollectionUtils.isNotEmpty(orderData)) {
                for (Map<String, String> order : orderData) {
                    // 根据OrderSearchResponse的返回结构解析订单数据
                    // 订单数据中可能包含dealId、productId等字段
                    String dealIdStr = order.get("dealId");
                    if (dealIdStr == null) {
                        // 如果没有dealId字段，尝试其他可能的字段名
                        dealIdStr = order.get("productId");
                    }
                    if (dealIdStr == null) {
                        dealIdStr = order.get("bizProductId");
                    }

                    if (dealIdStr != null) {
                        try {
                            Integer dealId = Integer.valueOf(dealIdStr);
                            if (dealIds.contains(dealId)) {
                                dealPurchaseCountMap.merge(dealId, 1, Integer::sum);
                            }
                        } catch (NumberFormatException e) {
                            log.debug("DealUserPurchaseBatchFetcher: 无法解析dealId: {}", dealIdStr);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("DealUserPurchaseBatchFetcher: 处理订单搜索数据失败", ex);
        }
    }
}
