package com.sankuai.dealtheme.core.fetchers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.fetchers.BatchFetcher;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.dealtheme.core.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户团单购买次数批量获取器
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Fetcher(name = "dealUserPurchase")
public class DealUserPurchaseBatchFetcher implements BatchFetcher<DealModel, DealUserPurchaseInfoModel> {

    @Autowired
    private FacadeService facadeService;

    @Override
    public CompletableFuture<Map<DealModel, DealUserPurchaseInfoModel>> batchFetch(
            List<DealModel> dealModels,
            @Param("execution") Execution execution) {
        
        if (CollectionUtils.isEmpty(dealModels)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取当前用户ID
        Long currentUserId = UserUtils.getCurrentUserId(execution);
        if (currentUserId == null || currentUserId <= 0) {
            log.warn("DealUserPurchaseBatchFetcher: 无法获取当前用户ID");
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取团单ID列表
        List<Integer> dealIds = dealModels.stream()
                .map(DealModel::getDealId)
                .collect(Collectors.toList());

        // 先转换用户ID（美团用户ID转点评用户ID）
        return facadeService.getRealBindByMtUserId(currentUserId)
                .thenCompose(dpUserId -> {
                    if (dpUserId == null || dpUserId <= 0) {
                        log.warn("DealUserPurchaseBatchFetcher: 无法转换用户ID, mtUserId:{}", currentUserId);
                        return CompletableFuture.completedFuture(Maps.newHashMap());
                    }

                    // 搜索用户订单信息（不区分平台，所以查询所有平台）
                    CompletableFuture<Map<String, Object>> mtOrdersFuture = facadeService.searchOrder(dpUserId, 1); // 美团平台
                    CompletableFuture<Map<String, Object>> dpOrdersFuture = facadeService.searchOrder(dpUserId, 2); // 点评平台

                    return CompletableFuture.allOf(mtOrdersFuture, dpOrdersFuture)
                            .thenApply(v -> {
                                Map<String, Object> mtOrders = mtOrdersFuture.join();
                                Map<String, Object> dpOrders = dpOrdersFuture.join();
                                
                                return buildUserPurchaseInfoMap(dealModels, dealIds, mtOrders, dpOrders);
                            });
                })
                .exceptionally(ex -> {
                    log.error("DealUserPurchaseBatchFetcher: 获取用户购买信息失败, userId:{}", currentUserId, ex);
                    return Maps.newHashMap();
                });
    }

    /**
     * 构建用户购买信息映射
     */
    private Map<DealModel, DealUserPurchaseInfoModel> buildUserPurchaseInfoMap(
            List<DealModel> dealModels,
            List<Integer> dealIds,
            Map<String, Object> mtOrders,
            Map<String, Object> dpOrders) {
        
        Map<DealModel, DealUserPurchaseInfoModel> resultMap = Maps.newHashMap();
        
        // 统计每个团单的购买次数
        Map<Integer, Integer> dealPurchaseCountMap = Maps.newHashMap();
        
        // 处理美团订单
        if (mtOrders != null) {
            processPlatformOrders(mtOrders, dealIds, dealPurchaseCountMap);
        }
        
        // 处理点评订单
        if (dpOrders != null) {
            processPlatformOrders(dpOrders, dealIds, dealPurchaseCountMap);
        }
        
        // 为每个团单构建购买信息模型
        for (DealModel dealModel : dealModels) {
            Integer dealId = dealModel.getDealId();
            Integer purchaseCount = dealPurchaseCountMap.getOrDefault(dealId, 0);
            
            DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
            purchaseInfo.setDealId(dealId);
            purchaseInfo.setPurchaseCount(purchaseCount);
            
            resultMap.put(dealModel, purchaseInfo);
        }
        
        return resultMap;
    }

    /**
     * 处理平台订单数据，统计购买次数
     */
    private void processPlatformOrders(Map<String, Object> orders, List<Integer> dealIds, Map<Integer, Integer> dealPurchaseCountMap) {
        try {
            // 根据OrderSearchService的返回结构解析订单数据
            // 这里需要根据实际的API返回结构进行调整
            if (orders.containsKey("orders")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> orderList = (List<Map<String, Object>>) orders.get("orders");
                
                if (CollectionUtils.isNotEmpty(orderList)) {
                    for (Map<String, Object> order : orderList) {
                        Object dealIdObj = order.get("dealId");
                        if (dealIdObj != null) {
                            Integer dealId = Integer.valueOf(dealIdObj.toString());
                            if (dealIds.contains(dealId)) {
                                dealPurchaseCountMap.merge(dealId, 1, Integer::sum);
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("DealUserPurchaseBatchFetcher: 处理订单数据失败", ex);
        }
    }
}
