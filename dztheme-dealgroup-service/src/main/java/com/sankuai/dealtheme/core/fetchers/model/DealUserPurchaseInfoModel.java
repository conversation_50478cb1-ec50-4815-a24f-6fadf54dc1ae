package com.sankuai.dealtheme.core.fetchers.model;

import com.sankuai.athena.theme.framework.annotations.Field;
import com.sankuai.athena.theme.framework.annotations.Type;
import lombok.Data;

/**
 * 用户团单购买信息模型
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Type(name = "DealUserPurchaseInfo", description = "用户团单购买信息")
public class DealUserPurchaseInfoModel {

    @Field(name = "dealId", description = "团单ID")
    private int dealId;

    @Field(name = "purchaseCount", description = "用户180天内购买次数")
    private int purchaseCount;
}
