package com.sankuai.dealtheme.core.fetchers.model;

import com.sankuai.athena.theme.framework.annotations.Field;
import com.sankuai.athena.theme.framework.annotations.Type;
import lombok.Data;

/**
 * @description :
 * @date : 2025/4/16
 */
@Data
@Type(name = "DealUserPurchaseInfoModel", description = "团单购买信息")
public class DealUserPurchaseInfoModel {
    @Field(name = "dealId", description = "团单id")
    private Integer dealId;
    @Field(name = "purchaseNums", description = "个人同门店团单购买次数")
    private int dealPurchaseNums;
}
